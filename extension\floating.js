/**
 * Binary Options Trading Bot - Floating Interface
 *
 * This script handles the floating interface that overlays on the Pocket Option website.
 * It communicates with the content script to execute trades and receive updates.
 * Each trading mode has a distinct visual appearance and functionality.
 */

// Global variables
let balance = 0; // Will be set from the real balance
let initialBalance = 0; // Will be set from the real balance
let realBalanceReceived = false; // Flag to track if we've received a real balance
let riskPerTrade = 0.02; // Default risk per trade (2%)
let currentMode = 'quantum'; // Default mode: quantum edge
let payoutPercentage = 85; // Default payout percentage
let currentCurrencyPair = 'EUR/USD'; // Default currency pair
let isAutoTrading = false; // Auto trading flag
let activeTrade = false; // Active trade flag
let autoTradeInterval; // Auto trading interval
let tradeTimeout; // Trade timeout
let currentTrade = null; // Current trade details

// Trading statistics
let totalTrades = 0;
let winningTrades = 0;
let losingTrades = 0;
let consecutiveWins = 0;
let consecutiveLosses = 0;

// DOM elements
const floatingContainer = document.getElementById('floatingContainer');
const lockBtn = document.getElementById('lockBtn');
const minimizeBtn = document.getElementById('minimizeBtn');
const closeBtn = document.getElementById('closeBtn');
const modeContentElement = document.getElementById('mode-content');

// Mode buttons
const quantumModeBtn = document.getElementById('mode-quantum');
const phoenixModeBtn = document.getElementById('mode-phoenix');
const neuralModeBtn = document.getElementById('mode-neural');

// State variables
let isLocked = false;

// Neural Pulse Money Management variables (same as Phoenix mode)
let neuralTradeResults = []; // Array to store W/L results for Neural Pulse
let neuralTradeHistory = []; // Trade history for Neural Pulse
let neuralTotalTrades = 10; // Total number of trades planned in the session
let neuralWinTrades = 3; // Number of win trades in the session
let neuralPayoutMultiplier = 1.85; // Payout multiplier (e.g., 85% payout = 1.85)
let neuralCurrentTradeAmount = 0; // Current trade amount for Neural Pulse
let neuralFixedInitialCapital = 0; // Fixed initial capital for Neural Pulse calculations

// Debug flag - set to true to enable console logging
const DEBUG = true;

/**
 * Debug logging function - only logs if DEBUG is true
 */
function debugLog(...args) {
    if (DEBUG) {
        console.log(...args);
    }
}

// We're now handling dragging at the container level in content.js
// No need to make the floating window draggable here

// Initialize the interface
function initializeInterface() {
    debugLog('Initializing floating interface');

    // Try to load the real balance from localStorage
    try {
        const savedBalance = localStorage.getItem('pocketOptionRealBalance');
        const savedTimestamp = localStorage.getItem('pocketOptionRealBalanceTimestamp');

        if (savedBalance && savedTimestamp) {
            const parsedBalance = parseFloat(savedBalance);
            const timestamp = parseInt(savedTimestamp);

            // Only use the saved balance if it's less than 1 hour old
            const oneHourAgo = Date.now() - (60 * 60 * 1000);

            if (!isNaN(parsedBalance) && timestamp > oneHourAgo) {
                debugLog('Loaded real balance from localStorage:', parsedBalance);
                balance = parsedBalance;
                initialBalance = parsedBalance;
                realBalanceReceived = true;

                // Update the UI
                updateBalance();
            }
        }
    } catch (e) {
        console.error('Failed to load real balance from localStorage:', e);
    }

    // Set up event listeners
    minimizeBtn.addEventListener('click', toggleMinimize);
    closeBtn.addEventListener('click', closeFloatingWindow);
    lockBtn.addEventListener('click', toggleLock);
    floatingContainer.addEventListener('click', function() {
        if (floatingContainer.classList.contains('minimized')) {
            toggleMinimize();
        }
    });

    // Mode selection event listeners
    quantumModeBtn.addEventListener('click', () => setMode('quantum'));
    phoenixModeBtn.addEventListener('click', () => setMode('phoenix'));
    neuralModeBtn.addEventListener('click', () => setMode('neural'));

    // Set the initial mode - this will also set up the mode-specific event listeners
    debugLog('Setting initial mode to:', currentMode);
    setMode(currentMode);

    // Double-check that mode-specific event listeners are set up
    debugLog('Ensuring mode-specific event listeners are set up');
    setupModeEventListeners();

    // Log that initialization is complete
    debugLog('Floating interface initialization complete');
}

// Load settings from storage
function loadSettings() {
    debugLog('Loading settings');

    // Send message to parent to get settings
    window.parent.postMessage({
        action: 'getSettings'
    }, '*');
}

// Load Neural Pulse specific settings
function loadNeuralPulseSettings() {
    debugLog('Loading Neural Pulse settings');

    try {
        const savedSettings = localStorage.getItem('neuralPulseSettings');
        if (savedSettings) {
            const settings = JSON.parse(savedSettings);

            // Restore Neural Pulse money management variables
            if (settings.neuralTradeResults) neuralTradeResults = settings.neuralTradeResults;
            if (settings.neuralTradeHistory) neuralTradeHistory = settings.neuralTradeHistory;
            if (settings.neuralTotalTrades) neuralTotalTrades = settings.neuralTotalTrades;
            if (settings.neuralWinTrades) neuralWinTrades = settings.neuralWinTrades;
            if (settings.neuralPayoutMultiplier) neuralPayoutMultiplier = settings.neuralPayoutMultiplier;
            if (settings.neuralCurrentTradeAmount) neuralCurrentTradeAmount = settings.neuralCurrentTradeAmount;
            if (settings.neuralFixedInitialCapital) neuralFixedInitialCapital = settings.neuralFixedInitialCapital;

            debugLog('Neural Pulse settings loaded:', {
                neuralTradeResults: neuralTradeResults.length,
                neuralTradeHistory: neuralTradeHistory.length,
                neuralTotalTrades,
                neuralWinTrades,
                neuralPayoutMultiplier,
                neuralCurrentTradeAmount,
                neuralFixedInitialCapital
            });
        }
    } catch (e) {
        console.error('Failed to load Neural Pulse settings:', e);
    }
}

// Basic UI functions
function toggleMinimize() {
    floatingContainer.classList.toggle('minimized');
}

function closeFloatingWindow() {
    // Send message to content script to remove the floating window
    window.parent.postMessage({ action: 'closeFloatingWindow' }, '*');
}

function toggleLock() {
    isLocked = !isLocked;

    // Update the lock button icon
    if (isLocked) {
        lockBtn.innerHTML = '<i class="fas fa-lock"></i>';
        lockBtn.title = 'Unlock Position';
        lockBtn.classList.add('locked');
    } else {
        lockBtn.innerHTML = '<i class="fas fa-lock-open"></i>';
        lockBtn.title = 'Lock Position';
        lockBtn.classList.remove('locked');
    }

    // Send message to parent to lock/unlock the container
    window.parent.postMessage({
        action: 'toggleLock',
        isLocked: isLocked
    }, '*');

    // Show notification
    showNotification(isLocked ? 'Position locked' : 'Position unlocked', 'info');
}

/**
 * Set the current trading mode and update the UI
 */
function setMode(mode) {
    debugLog('Setting mode to:', mode);
    currentMode = mode;

    // Update UI - remove active class from all mode buttons
    quantumModeBtn.classList.remove('active');
    phoenixModeBtn.classList.remove('active');
    neuralModeBtn.classList.remove('active');

    // Add active class to the selected mode button
    if (mode === 'quantum') {
        quantumModeBtn.classList.add('active');
    }
    else if (mode === 'phoenix') {
        phoenixModeBtn.classList.add('active');
    }
    else if (mode === 'neural') {
        neuralModeBtn.classList.add('active');
    }

    // Update the mode-specific content
    updateModeContent(mode);

    // Set up event listeners for the mode-specific elements
    // This is critical to ensure buttons work after mode change
    debugLog('Setting up event listeners for mode:', mode);
    setupModeEventListeners();



    // Save settings
    saveSettings();

    // Send message to content script
    window.parent.postMessage({
        action: 'setMode',
        mode: mode
    }, '*');
}

/**
 * Update the mode-specific content based on the selected mode
 */
function updateModeContent(mode) {
    // Remove any existing mode classes
    modeContentElement.classList.remove('quantum-mode', 'phoenix-mode', 'neural-mode');

    // Clear existing content
    modeContentElement.innerHTML = '';

    // Add the appropriate mode class
    modeContentElement.classList.add(`${mode}-mode`);

    // Create mode-specific content with only the heading
    let content = '';

    if (mode === 'quantum') {
        // Quantum Edge Mode - Manual Signal Trading
        modeContentElement.classList.add('quantum-mode');

        // Calculate suggested values
        const suggestedRiskCapital = balance * 0.5;
        const suggestedTargetProfit = suggestedRiskCapital * 0.2;

        content = `
            <div class="mode-header">
                <i class="fas fa-atom"></i> QUANTUM EDGE
            </div>

            <div class="balance-display">
                <div class="balance-label">Current Balance</div>
                <div class="balance-value">$${balance.toFixed(2)}</div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Initial Balance</div>
                <div class="setting-value">
                    <div class="risk-input-container">
                        <input type="number" id="quantumInitialBalance" class="setting-input" value="${balance.toFixed(2)}" step="0.01" min="1">
                    </div>
                    <div class="suggestion">Set your starting balance</div>
                </div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Risk Percentage</div>
                <div class="setting-value">
                    <div class="risk-input-container">
                        <input type="number" id="quantumRiskPercentage" class="setting-input" value="1" step="0.1" min="0.1" max="5">
                    </div>
                    <div class="suggestion">Risk per trade (0.1% - 5%)</div>
                </div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Payout Percentage</div>
                <div class="setting-value">
                    <div class="risk-input-container">
                        <input type="number" id="quantumPayoutPercentage" class="setting-input" value="85" step="1" min="70" max="95">
                    </div>
                    <div class="suggestion">Broker payout rate</div>
                </div>
            </div>

            <div class="setting-group">
                <div class="setting-label">Profit Target</div>
                <div class="setting-value">
                    <div class="risk-input-container">
                        <input type="number" id="quantumProfitTarget" class="setting-input" value="" step="0.01" min="1">
                    </div>
                    <div class="suggestion">Target profit amount (optional)</div>
                </div>
            </div>

            <div class="trading-controls">
                <button id="quantumStartStopBtn" class="action-btn">
                    <i class="fas fa-play"></i> START QUANTUM
                </button>
                <button id="quantumPauseResumeBtn" class="action-btn" disabled>
                    <i class="fas fa-pause"></i> PAUSE
                </button>
            </div>

            <div class="trading-status">
                <div id="quantumTradingStatus" class="status-text">Ready to start signal generation</div>
                <div id="quantumCurrentTradeAmount" class="trade-amount">Next trade: $0.00</div>
            </div>

            <!-- Signal Display -->
            <div class="signal-display" id="quantumSignalDisplay" style="display: none;">
                <div class="signal-header">
                    <i class="fas fa-satellite-dish"></i> QUANTUM SIGNAL DETECTED
                </div>
                <div class="signal-content">
                    <div class="signal-item">
                        <span class="signal-label">Direction:</span>
                        <span class="signal-value" id="quantumSignalDirection">CALL</span>
                    </div>
                    <div class="signal-item">
                        <span class="signal-label">Strength:</span>
                        <span class="signal-value" id="quantumSignalStrength">87%</span>
                    </div>
                    <div class="signal-item">
                        <span class="signal-label">Expiry:</span>
                        <span class="signal-value" id="quantumSignalExpiry">5 min</span>
                    </div>
                </div>
            </div>

            <!-- Manual Result Buttons -->
            <div class="result-buttons" id="quantumResultButtons" style="display: none;">
                <div class="result-header">
                    <i class="fas fa-hand-pointer"></i> ENTER TRADE RESULT
                </div>
                <div class="result-controls">
                    <button id="quantumWinBtn" class="result-btn win-btn">
                        <i class="fas fa-check-circle"></i> WIN
                    </button>
                    <button id="quantumLoseBtn" class="result-btn lose-btn">
                        <i class="fas fa-times-circle"></i> LOSS
                    </button>
                </div>
                <div class="result-instruction">
                    Take the signal manually and click the result after trade completion
                </div>
            </div>

            <div class="quantum-stats">
                <div class="stat-item">
                    <div class="stat-label">Win Rate</div>
                    <div id="quantumWinRate" class="stat-value">0%</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Profit Factor</div>
                    <div id="quantumProfitFactor" class="stat-value">0.00</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">Total Trades</div>
                    <div id="quantumTotalTrades" class="stat-value">0</div>
                </div>
            </div>
        `;
    }

    else if (mode === 'neural') {
        // Neural Pulse Mode - Direct content instead of iframe
        content = `
            <div class="neural-pulse-container">
                <div class="setting-group">
                    <div class="setting-label">Neural Network Status</div>
                    <div class="ai-status">
                        <i class="fas fa-brain"></i>
                        <span id="aiStatusText">Ready to analyze market</span>
                    </div>

                    <div class="ai-confidence-container">
                        <div class="ai-confidence-label">
                            <span>Confidence Level</span>
                            <span class="ai-confidence-value">75%</span>
                        </div>
                        <div class="ai-confidence">
                            <div class="ai-confidence-bar" id="aiConfidenceBar"></div>
                        </div>
                    </div>

                    <div class="ai-message" id="aiMessage">
                        Neural network initialized and ready for trading decisions.
                    </div>
                </div>

                <!-- Trading Controls -->
                <div class="trading-controls">
                    <button id="startStopBtn" class="action-btn"><i class="fas fa-play"></i> Activate AI</button>
                    <button id="pauseResumeBtn" class="action-btn" disabled><i class="fas fa-pause"></i> Pause</button>
                </div>

                <!-- Trading Status Panel -->
                <div class="trading-status">
                    <div id="tradingStatus" class="status-text">AI ready to analyze market patterns</div>
                    <div id="nextAction" class="trade-amount">Waiting for AI activation...</div>
                </div>

                <!-- Settings Panels -->
                <div class="setting-group">
                    <div class="setting-label">AI Autonomy Mode</div>
                    <div class="setting-value">
                        <div class="risk-input-container">
                            <select id="autonomyLevel" class="setting-input">
                                <option value="assisted">Assisted (Confirm Trades)</option>
                                <option value="semi" selected>Semi-Autonomous</option>
                                <option value="full">Fully Autonomous</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-label">Risk Tolerance</div>
                    <div class="setting-value">
                        <div class="suggestion">Higher risk = larger position sizing</div>
                        <div class="risk-input-container">
                            <select id="riskLevel" class="setting-input">
                                <option value="low">Conservative</option>
                                <option value="medium" selected>Balanced</option>
                                <option value="high">Aggressive</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-label">Target Profit ($)</div>
                    <div class="setting-value">
                        <div class="suggestion">Enter your desired profit target</div>
                        <div class="risk-input-container">
                            <input type="number" id="targetProfitInput" class="setting-input" value="" min="1" step="0.01" placeholder="e.g. 100.00">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Set the content
    modeContentElement.innerHTML = content;

    // If Phoenix mode, ensure the container's scrollbar is updated
    if (mode === 'phoenix') {
        // Force the floating container to update its scroll area
        setTimeout(() => {
            const floatingContainer = document.getElementById('floatingContainer');
            if (floatingContainer) {
                // Force a reflow to update the scrollbar
                floatingContainer.style.display = 'none';
                floatingContainer.offsetHeight; // Force reflow
                floatingContainer.style.display = '';
            }
        }, 1000);
    }
}

/**
 * Place a trade
 */
function placeTrade(direction) {
    debugLog('placeTrade called with direction:', direction);

    if (activeTrade) {
        showNotification('Cannot place new trade, active trade in progress', 'error');
        debugLog('Cannot place trade - active trade in progress');
        return;
    }

    // Determine trade amount based on mode
    let tradeAmount;
    let expiry = 60; // Default 1 minute

    if (currentMode === 'neural') {
        // Calculate trade amount based on strategy
        tradeAmount = calculatePositionSize();
        expiry = 300; // 5 minutes for neural mode
    } else {
        // Calculate trade amount based on strategy for other modes
        tradeAmount = calculatePositionSize();
    }

    // Set activeTrade flag to prevent multiple trades
    activeTrade = true;

    // Log the trade details
    debugLog('Trade details:', {
        direction,
        amount: tradeAmount,
        expiry,
        mode: currentMode
    });

    // Record the start time
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + expiry * 1000);

    // Store current trade details
    currentTrade = {
        direction,
        amount: tradeAmount,
        expiry,
        startTime,
        endTime
    };

    // Update trade info
    updateTradeInfo(direction, tradeAmount, expiry, startTime, endTime);

    // Send message to content script to execute trade
    debugLog('Sending executeTrade message to content script:', {
        direction,
        amount: tradeAmount,
        expiry,
        mode: currentMode
    });

    try {
        // Create a more visible notification
        const notificationText = `Placing ${direction} trade for $${tradeAmount.toFixed(2)}`;
        showNotification(notificationText, 'info');

        // Update UI to show trade is in progress
        const tradingStatus = document.getElementById('tradingStatus');
        if (tradingStatus) {
            tradingStatus.textContent = `Executing ${direction} trade for $${tradeAmount.toFixed(2)}...`;
        }

        // Add a small delay before sending the message to ensure the UI is updated
        setTimeout(() => {
            try {
                // Send the message to the parent window (content script)
                debugLog('Sending executeTrade message to parent window');
                window.parent.postMessage({
                    action: 'executeTrade',
                    direction: direction,
                    amount: tradeAmount,
                    expiry: expiry,
                    mode: currentMode,
                    timestamp: Date.now() // Add timestamp to ensure message uniqueness
                }, '*');

                debugLog('Trade execution message sent successfully');

                // Set up a timeout to reset the active trade flag if no response is received
                // This prevents the bot from getting stuck if the trade execution fails
                setTimeout(() => {
                    if (activeTrade && currentTrade &&
                        currentTrade.direction === direction &&
                        currentTrade.amount === tradeAmount) {
                        debugLog('No trade result received within timeout period, resetting active trade flag');
                        showNotification('Trade may have failed, resetting to allow new trades', 'warning');
                        activeTrade = false;
                        currentTrade = null;

                        // Update UI
                        if (tradingStatus) {
                            tradingStatus.textContent = 'Trade timed out, ready for next trade';
                        }

                        // Force a new trade cycle to continue trading
                        if (isTrading && !isPaused) {
                            debugLog('Forcing new trade cycle after timeout');
                            setTimeout(startTradingCycle, 5000); // Wait 5 seconds before starting a new cycle
                        }
                    }
                }, 30000); // Use a fixed 30 second timeout instead of waiting for expiry
            } catch (innerError) {
                debugLog('Error sending trade execution message:', innerError);
                showNotification(`Error sending trade: ${innerError.message}`, 'error');

                // Reset active trade flag
                activeTrade = false;
                currentTrade = null;
            }
        }, 1000); // Increased delay to 1 second
    } catch (error) {
        debugLog('Error in placeTrade:', error);
        showNotification(`Error placing trade: ${error.message}`, 'error');

        // Reset active trade flag
        activeTrade = false;
        currentTrade = null;
    }
}

function calculatePositionSize() {
    let positionSize;

    if (currentMode === 'neural') {
        // Neural Pulse mode now uses the same money management as Phoenix mode
        positionSize = calculateNeuralMasanielloStake();
    }
    else {
        // Default mode - use basic risk percentage
        positionSize = balance * riskPerTrade;
    }

    return Math.round(positionSize * 100) / 100; // Round to 2 decimal places
}

function getWinRateForCurrentMode() {
    switch (currentMode) {
        case 'quantum': return 0.75;  // 75% win rate
        case 'phoenix': return 0.75;  // 75% win rate
        case 'neural': return 0.90;   // 90% win rate
        default: return 0.55;
    }
}

// Calculate the stake amount using the Money Management System for Neural Pulse mode
function calculateNeuralMasanielloStake() {
    // Initialize Neural Pulse money management if not already done
    if (neuralFixedInitialCapital === 0) {
        neuralFixedInitialCapital = balance;
        debugLog('Neural Pulse: Initialized fixed initial capital:', neuralFixedInitialCapital);
    }

    // Current state
    const w = neuralTradeResults.filter(result => result === 'win').length; // Wins so far
    const l = neuralTradeResults.filter(result => result === 'loss').length; // Losses so far
    const i = neuralTradeResults.length + 1; // Current trade number (1-based)

    // Money Management System parameters
    const N = neuralTotalTrades; // Total trades
    const K = neuralWinTrades; // Target wins
    const B = neuralFixedInitialCapital; // Initial capital (fixed)
    const returnPerUnit = neuralPayoutMultiplier - 1; // Return per unit (e.g., 0.85 for 85% payout)

    // Current portfolio calculation
    let currentPortfolio = neuralFixedInitialCapital;

    // Calculate current portfolio based on trade history
    for (let j = 0; j < neuralTradeHistory.length; j++) {
        const trade = neuralTradeHistory[j];
        if (trade.result === 'win') {
            currentPortfolio += trade.amount * returnPerUnit;
        } else {
            currentPortfolio -= trade.amount;
        }
    }

    debugLog('Neural Pulse Money Management calculation parameters:', {
        tradeNumber: i,
        totalTrades: N,
        targetWins: K,
        currentWins: w,
        currentLosses: l,
        initialCapital: B,
        currentPortfolio: currentPortfolio,
        returnPerUnit: returnPerUnit
    });

    // Check if the previous trade was a loss
    let stake;
    if (neuralTradeResults.length > 0 && neuralTradeResults[neuralTradeResults.length - 1] === 'loss') {
        // After a loss, calculate stake to recover half of the previous loss
        const previousTradeAmount = neuralTradeHistory.length > 0 ? neuralTradeHistory[neuralTradeHistory.length - 1].amount : 0;

        // Calculate the amount needed to recover half of the previous loss
        const halfLoss = previousTradeAmount / 2;

        // Calculate the stake needed to win half the previous loss
        stake = halfLoss / returnPerUnit;

        debugLog('Neural Pulse: Recovery stake calculation after loss:', {
            previousTradeAmount,
            halfLoss,
            returnPerUnit,
            calculatedStake: stake
        });
    } else {
        // Normal case: Calculate risk percentage based on risk tolerance setting
        let riskPercentage = 0.03; // Default conservative

        // Get risk level from UI
        const riskLevelElement = document.getElementById('riskLevel');
        if (riskLevelElement) {
            const riskLevel = riskLevelElement.value;
            switch (riskLevel) {
                case 'low':
                    riskPercentage = 0.02; // Conservative: 2%
                    break;
                case 'medium':
                    riskPercentage = 0.035; // Balanced: 3.5%
                    break;
                case 'high':
                    riskPercentage = 0.05; // Aggressive: 5%
                    break;
                default:
                    riskPercentage = 0.03; // Default
            }
        }

        // Calculate trade amount as a percentage of the current portfolio
        stake = currentPortfolio * riskPercentage;

        debugLog('Neural Pulse: Normal stake calculation:', {
            currentPortfolio,
            riskLevel: riskLevelElement ? riskLevelElement.value : 'unknown',
            riskPercentage,
            calculatedStake: stake
        });
    }

    // Use the calculated stake directly
    const finalStake = stake;

    debugLog('Neural Pulse Money Management calculation result:', {
        tradeNumber: i,
        currentWins: w,
        currentLosses: l,
        calculatedStake: stake,
        finalStake
    });

    // Store the current trade amount for Neural Pulse
    neuralCurrentTradeAmount = finalStake;

    return finalStake;
}

// Handle Neural Pulse trade result and update money management system
function handleNeuralTradeResult(result) {
    debugLog('Neural Pulse: Handling trade result:', result);

    // Record the trade result
    const resultString = result === 'WIN' ? 'win' : 'loss';
    neuralTradeResults.push(resultString);

    // Add to Neural Pulse trade history
    neuralTradeHistory.push({
        id: neuralTradeHistory.length + 1,
        direction: currentTrade ? currentTrade.direction : 'UNKNOWN',
        amount: neuralCurrentTradeAmount,
        result: resultString,
        balance: balance
    });

    debugLog('Neural Pulse: Trade added to history:', {
        tradeNumber: neuralTradeHistory.length,
        direction: currentTrade ? currentTrade.direction : 'UNKNOWN',
        amount: neuralCurrentTradeAmount,
        result: resultString,
        balance: balance
    });

    // Check if we've reached the profit target
    const targetProfitInput = document.getElementById('targetProfitInput');
    if (targetProfitInput && targetProfitInput.value) {
        const targetProfit = parseFloat(targetProfitInput.value);
        const currentProfit = balance - neuralFixedInitialCapital;

        if (currentProfit >= targetProfit) {
            // Stop trading and show congratulations
            isTrading = false;
            isPaused = false;

            // Update UI
            const startStopBtn = document.getElementById('startStopBtn');
            const pauseResumeBtn = document.getElementById('pauseResumeBtn');
            const aiStatusText = document.getElementById('aiStatusText');
            const tradingStatus = document.getElementById('tradingStatus');

            if (startStopBtn) {
                startStopBtn.innerHTML = '<i class="fas fa-play"></i> Activate AI';
                startStopBtn.classList.remove('stop');
            }
            if (pauseResumeBtn) {
                pauseResumeBtn.disabled = true;
            }
            if (aiStatusText) {
                aiStatusText.textContent = 'Target reached!';
            }
            if (tradingStatus) {
                tradingStatus.textContent = 'Profit target achieved';
            }

            // Show congratulations popup
            showCongratulationsPopup(currentProfit);

            debugLog('Neural Pulse: Profit target reached!', {
                targetProfit,
                currentProfit,
                balance,
                initialCapital: neuralFixedInitialCapital
            });

            return; // Exit early, don't continue trading
        }
    }

    // Check if we've reached the target profit or completed all trades
    if (neuralTradeResults.length >= neuralTotalTrades) {
        debugLog('Neural Pulse: Completed all planned trades');
        // Could add logic here to stop trading or reset the cycle
    }
}

function handleTradeResult(result) {
    // Don't check activeTrade flag here since we've already reset it
    // This ensures we always process the trade result

    // Log the current state
    debugLog('In handleTradeResult - result:', result, 'currentTrade:', currentTrade);

    // Clear any pending timeout
    if (tradeTimeout) {
        clearTimeout(tradeTimeout);
        tradeTimeout = null;
    }

    // Make sure we have a current trade to process
    if (!currentTrade) {
        debugLog('No current trade to process in handleTradeResult');
        return;
    }

    const tradeAmount = currentTrade.amount;

    // Update statistics
    totalTrades++;

    if (result === 'WIN') {
        winningTrades++;
        consecutiveWins++;
        consecutiveLosses = 0;

        // Only update simulated balance if we haven't received a real balance
        if (!realBalanceReceived) {
            const profit = tradeAmount * (payoutPercentage / 100);
            balance += profit;
            if (currentMode !== 'neural') {
                showNotification(`Trade WON: +$${profit.toFixed(2)}`, 'success');
            }
        } else {
            if (currentMode !== 'neural') {
                showNotification(`Trade WON! Check your real balance on Pocket Option`, 'success');
            }
        }
    } else {
        losingTrades++;
        consecutiveLosses++;
        consecutiveWins = 0;

        // Only update simulated balance if we haven't received a real balance
        if (!realBalanceReceived) {
            balance -= tradeAmount;
            if (currentMode !== 'neural') {
                showNotification(`Trade LOST: -$${tradeAmount.toFixed(2)}`, 'error');
            }
        } else {
            if (currentMode !== 'neural') {
                showNotification(`Trade LOST! Check your real balance on Pocket Option`, 'error');
            }
        }
    }

    // Reset active trade
    activeTrade = false;
    currentTrade = null;

    // Update UI
    updateTradeInfo();
    updateStatistics();

    // Only update balance display if we haven't received a real balance
    if (!realBalanceReceived) {
        updateBalance();
    }

    // Handle mode-specific trade result
    if (currentMode === 'neural' && isTrading) {
        handleNeuralTradeResult(result);
    } else if (currentMode === 'quantum' && window.quantumBot && window.quantumBot.isRunning) {
        // Route trade result to quantum bot
        window.quantumBot.handleTradeResult(result);
    }

    saveSettings();

    // Continue auto trading if enabled
    if (isAutoTrading) {
        // Default auto interval time (in seconds)
        const autoIntervalTime = 30;

        autoTradeInterval = setTimeout(() => {
            if (!activeTrade) {
                placeTrade(Math.random() < 0.5 ? 'BUY' : 'SELL');
            }
        }, autoIntervalTime * 1000);
    }
}

/**
 * Toggle auto trading mode
 */
function toggleAutoTrading() {
    isAutoTrading = !isAutoTrading;

    // Get the auto trading button
    const autoTradingBtn = document.getElementById('autoTrading');
    if (!autoTradingBtn) {
        debugLog('Auto trading button not found');
        return;
    }

    if (isAutoTrading) {
        autoTradingBtn.textContent = 'STOP';
        autoTradingBtn.classList.add('active');

        // Start first trade immediately
        if (!activeTrade) {
            placeTrade(Math.random() < 0.5 ? 'BUY' : 'SELL');
        }

        showNotification(`Auto trading started`, 'info');
    } else {
        autoTradingBtn.innerHTML = '<i class="fas fa-robot"></i> AUTO';
        autoTradingBtn.classList.remove('active');

        // Clear auto trading interval
        if (autoTradeInterval) {
            clearTimeout(autoTradeInterval);
            autoTradeInterval = null;
        }

        showNotification('Auto trading stopped', 'info');
    }

    saveSettings();
}

/**
 * Save settings to storage
 */
function saveSettings() {
    // Send message to parent to save settings
    window.parent.postMessage({
        action: 'saveSettings',
        settings: {
            balance: balance,
            initialBalance: initialBalance,
            riskPerTrade: riskPerTrade,
            currentMode: currentMode,
            isAutoTrading: isAutoTrading,
            totalTrades: totalTrades,
            winningTrades: winningTrades,
            losingTrades: losingTrades,
            consecutiveWins: consecutiveWins,
            consecutiveLosses: consecutiveLosses,
            riskCapital: riskCapital,
            targetProfit: targetProfit,
            profitLoss: profitLoss,
            currentTradeAmount: currentTradeAmount,
            initialTradeAmount: initialTradeAmount,
            // Neural Pulse money management variables
            neuralTradeResults: neuralTradeResults,
            neuralTradeHistory: neuralTradeHistory,
            neuralTotalTrades: neuralTotalTrades,
            neuralWinTrades: neuralWinTrades,
            neuralPayoutMultiplier: neuralPayoutMultiplier,
            neuralCurrentTradeAmount: neuralCurrentTradeAmount,
            neuralFixedInitialCapital: neuralFixedInitialCapital
        }
    }, '*');

    // Also save Neural Pulse specific settings to localStorage
    try {
        const neuralPulseSettings = {
            neuralTradeResults: neuralTradeResults,
            neuralTradeHistory: neuralTradeHistory,
            neuralTotalTrades: neuralTotalTrades,
            neuralWinTrades: neuralWinTrades,
            neuralPayoutMultiplier: neuralPayoutMultiplier,
            neuralCurrentTradeAmount: neuralCurrentTradeAmount,
            neuralFixedInitialCapital: neuralFixedInitialCapital
        };
        localStorage.setItem('neuralPulseSettings', JSON.stringify(neuralPulseSettings));
    } catch (e) {
        console.error('Failed to save Neural Pulse settings to localStorage:', e);
    }
}

/**
 * Show a notification
 */
function showNotification(message, type) {
    // Send message to parent to show notification
    window.parent.postMessage({
        action: 'showNotification',
        message: message,
        type: type
    }, '*');
}

/**
 * Set up event listeners for Quantum Edge mode
 */
function setupQuantumModeListeners() {
    const startStopBtn = document.getElementById('quantumStartStopBtn');
    const pauseResumeBtn = document.getElementById('quantumPauseResumeBtn');
    const tradingStatus = document.getElementById('quantumTradingStatus');
    const currentTradeAmountElement = document.getElementById('quantumCurrentTradeAmount');

    // Input elements
    const initialBalanceInput = document.getElementById('quantumInitialBalance');
    const riskPercentageInput = document.getElementById('quantumRiskPercentage');
    const payoutPercentageInput = document.getElementById('quantumPayoutPercentage');
    const profitTargetInput = document.getElementById('quantumProfitTarget');

    // Signal and result elements
    const signalDisplay = document.getElementById('quantumSignalDisplay');
    const resultButtons = document.getElementById('quantumResultButtons');
    const winBtn = document.getElementById('quantumWinBtn');
    const loseBtn = document.getElementById('quantumLoseBtn');

    if (!startStopBtn || !pauseResumeBtn) {
        debugLog('Quantum mode buttons not found');
        return;
    }

    // Initialize quantum trading bot instance
    if (!window.quantumBot) {
        window.quantumBot = new QuantumTradingBot();
    }

    const bot = window.quantumBot;

    // Set up input event listeners
    if (initialBalanceInput) {
        initialBalanceInput.addEventListener('change', () => {
            const newBalance = parseFloat(initialBalanceInput.value);
            if (!isNaN(newBalance) && newBalance > 0) {
                bot.setBalance(newBalance);
                debugLog('Initial balance updated to:', newBalance);
            }
        });
    }

    if (riskPercentageInput) {
        riskPercentageInput.addEventListener('change', () => {
            const newRisk = parseFloat(riskPercentageInput.value);
            if (!isNaN(newRisk) && newRisk > 0 && newRisk <= 5) {
                bot.setRiskPercentage(newRisk);
                debugLog('Risk percentage updated to:', newRisk);
            }
        });
    }

    if (payoutPercentageInput) {
        payoutPercentageInput.addEventListener('change', () => {
            const newPayout = parseFloat(payoutPercentageInput.value);
            if (!isNaN(newPayout) && newPayout > 0) {
                bot.setPayoutPercentage(newPayout);
                debugLog('Payout percentage updated to:', newPayout);
            }
        });
    }

    if (profitTargetInput) {
        profitTargetInput.addEventListener('change', () => {
            const newTarget = parseFloat(profitTargetInput.value);
            bot.setProfitTarget(newTarget || 0);
            debugLog('Profit target updated to:', newTarget);
        });
    }

    // Start/Stop button
    startStopBtn.addEventListener('click', () => {
        if (!bot.isRunning) {
            if (bot.balance <= 0) {
                showNotification('Please set initial balance first', 'error');
                return;
            }
            bot.start();
            startStopBtn.innerHTML = '<i class="fas fa-stop"></i> STOP QUANTUM';
            startStopBtn.classList.add('stop');
            pauseResumeBtn.disabled = false;
            tradingStatus.textContent = 'Quantum system active - Analyzing markets...';
            showNotification('Quantum Edge system started', 'success');
        } else {
            bot.stop();
            startStopBtn.innerHTML = '<i class="fas fa-play"></i> START QUANTUM';
            startStopBtn.classList.remove('stop');
            pauseResumeBtn.disabled = true;
            tradingStatus.textContent = 'Ready to start adaptive trading';
            showNotification('Quantum Edge system stopped', 'info');
        }
    });

    // Pause/Resume button
    pauseResumeBtn.addEventListener('click', () => {
        if (!bot.isRunning) return;

        if (!bot.isPaused) {
            bot.pause();
            pauseResumeBtn.innerHTML = '<i class="fas fa-play"></i> RESUME';
            pauseResumeBtn.classList.add('resume');
            tradingStatus.textContent = 'Quantum system paused';
            showNotification('Quantum system paused', 'info');
        } else {
            bot.resume();
            pauseResumeBtn.innerHTML = '<i class="fas fa-pause"></i> PAUSE';
            pauseResumeBtn.classList.remove('resume');
            tradingStatus.textContent = 'Quantum system active - Analyzing markets...';
            showNotification('Quantum system resumed', 'success');
        }
    });

    // Win/Lose buttons for manual result input
    if (winBtn) {
        winBtn.addEventListener('click', () => {
            if (bot.waitingForResult) {
                bot.handleManualResult('W');
            }
        });
    }

    if (loseBtn) {
        loseBtn.addEventListener('click', () => {
            if (bot.waitingForResult) {
                bot.handleManualResult('L');
            }
        });
    }

    // Set initial balance from current balance
    if (balance > 0) {
        bot.setBalance(balance);
        if (initialBalanceInput) {
            initialBalanceInput.value = balance.toFixed(2);
        }
    }
}

/**
 * Set up event listeners for the mode-specific elements
 */
function setupModeEventListeners() {
    // Get the mode-specific elements
    if (currentMode === 'quantum') {
        setupQuantumModeListeners();

    } else if (currentMode === 'neural') {
        setupNeuralPulseListeners();
    }
}



/**
 * Set up event listeners for Neural Pulse mode
 */
function setupNeuralPulseListeners() {
    debugLog('Setting up Neural Pulse listeners');

    // Get Neural Pulse elements
    const startStopBtn = document.getElementById('startStopBtn');
    const pauseResumeBtn = document.getElementById('pauseResumeBtn');
    const tradingStatus = document.getElementById('tradingStatus');
    const nextAction = document.getElementById('nextAction');
    const aiStatusText = document.getElementById('aiStatusText');
    const aiMessage = document.getElementById('aiMessage');
    // aiConfidenceBar is used in simulateAIConfidence function
    document.getElementById('aiConfidenceBar');
    const autonomyLevel = document.getElementById('autonomyLevel');
    const riskLevel = document.getElementById('riskLevel');
    const targetProfitInput = document.getElementById('targetProfitInput');

    if (!startStopBtn || !pauseResumeBtn) {
        debugLog('Neural Pulse buttons not found');
        return;
    }

    // Set initial target profit value (no default)
    if (targetProfitInput) {

        // Add event listener for target profit changes
        targetProfitInput.addEventListener('change', () => {
            const newValue = parseFloat(targetProfitInput.value);
            if (!isNaN(newValue) && newValue > 0) {
                targetProfit = newValue;
                debugLog('Neural Pulse target profit updated to:', targetProfit);
                showNotification(`Target profit updated to $${targetProfit.toFixed(2)}`, 'info');
                saveSettings();
            }
        });
    }

    // Start/Stop button
    startStopBtn.addEventListener('click', () => {
        debugLog('Neural Pulse Start/Stop button clicked. Current state - isTrading:', isTrading);

        if (!isTrading) {
            // Start AI trading
            isTrading = true;
            isPaused = false;
            startStopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop AI';
            startStopBtn.classList.add('stop');
            pauseResumeBtn.disabled = false;

            if (aiStatusText) {
                aiStatusText.textContent = 'Analyzing market data';
            }

            if (tradingStatus) {
                tradingStatus.textContent = 'AI trading active';
            }

            if (nextAction) {
                nextAction.textContent = 'Calculating optimal entry point...';
            }

            if (aiMessage) {
                aiMessage.textContent = 'Neural network activated. Analyzing price patterns and market conditions to identify high-probability trading opportunities.';
            }

            // Disable settings during trading
            if (autonomyLevel) autonomyLevel.disabled = true;
            if (riskLevel) riskLevel.disabled = true;
            if (targetProfitInput) targetProfitInput.disabled = true;

            // Show a notification that AI trading is starting
            showNotification('Neural Pulse AI activated', 'success');

            // Simulate AI confidence changes
            simulateAIConfidence();

            // Start the AI trading cycle with a small delay
            setTimeout(() => {
                debugLog('Starting Neural Pulse trading cycle');
                startNeuralTradingCycle();
            }, 3000);
        } else {
            // Stop AI trading
            isTrading = false;
            isPaused = false;
            startStopBtn.innerHTML = '<i class="fas fa-play"></i> Activate AI';
            startStopBtn.classList.remove('stop');
            pauseResumeBtn.disabled = true;
            pauseResumeBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            pauseResumeBtn.classList.remove('resume');

            if (aiStatusText) {
                aiStatusText.textContent = 'Ready to analyze market';
            }

            if (tradingStatus) {
                tradingStatus.textContent = 'AI ready to analyze market patterns';
            }

            if (nextAction) {
                nextAction.textContent = 'Waiting for AI activation...';
            }

            if (aiMessage) {
                aiMessage.textContent = 'Neural network initialized and ready for trading decisions.';
            }

            // Re-enable settings
            if (autonomyLevel) autonomyLevel.disabled = false;
            if (riskLevel) riskLevel.disabled = false;
            if (targetProfitInput) targetProfitInput.disabled = false;

            // Clear any trading intervals
            if (tradingInterval) {
                clearTimeout(tradingInterval);
                tradingInterval = null;
            }

            showNotification('Neural Pulse AI deactivated', 'info');
        }
    });

    // Pause/Resume button
    pauseResumeBtn.addEventListener('click', () => {
        if (!isTrading) return;

        if (!isPaused) {
            // Pause AI trading
            isPaused = true;
            pauseResumeBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
            pauseResumeBtn.classList.add('resume');

            if (tradingStatus) {
                tradingStatus.textContent = 'AI trading paused';
            }

            if (aiStatusText) {
                aiStatusText.textContent = 'AI paused';
            }

            // Clear any trading intervals
            if (tradingInterval) {
                clearTimeout(tradingInterval);
                tradingInterval = null;
            }

            showNotification('Neural Pulse AI paused', 'info');
        } else {
            // Resume AI trading
            isPaused = false;
            pauseResumeBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
            pauseResumeBtn.classList.remove('resume');

            if (tradingStatus) {
                tradingStatus.textContent = 'AI trading active';
            }

            if (aiStatusText) {
                aiStatusText.textContent = 'Analyzing market data';
            }

            // Restart the AI trading cycle
            startNeuralTradingCycle();

            showNotification('Neural Pulse AI resumed', 'info');
        }
    });
}

/**
 * Simulate AI confidence changes for Neural Pulse mode
 * This function is called periodically to update the confidence bar
 * and confidence value in the Neural Pulse interface
 */
function simulateAIConfidence() {
    if (!isTrading || currentMode !== 'neural') return;

    const aiConfidenceBar = document.getElementById('aiConfidenceBar');
    const aiConfidenceValue = document.querySelector('.ai-confidence-value');

    if (!aiConfidenceBar || !aiConfidenceValue) return;

    // Generate a random confidence level between 65% and 95%
    const confidenceLevel = Math.floor(Math.random() * 31) + 65;

    // Update the confidence bar width and value
    aiConfidenceBar.style.width = `${confidenceLevel}%`;
    aiConfidenceValue.textContent = `${confidenceLevel}%`;

    // Schedule the next update
    setTimeout(simulateAIConfidence, 5000 + Math.random() * 10000); // Random interval between 5-15 seconds
}

/**
 * Start the Neural Pulse trading cycle
 */
function startNeuralTradingCycle() {
    if (!isTrading || isPaused || currentMode !== 'neural' || activeTrade) return;

    const tradingStatus = document.getElementById('tradingStatus');
    const nextAction = document.getElementById('nextAction');
    const aiStatusText = document.getElementById('aiStatusText');
    const aiMessage = document.getElementById('aiMessage');

    // Calculate a random delay for "AI analysis"
    const analysisTime = 5000 + Math.random() * 15000; // 5-20 seconds

    if (aiStatusText) {
        aiStatusText.textContent = 'Deep analysis in progress';
    }

    if (nextAction) {
        nextAction.textContent = 'Calculating optimal trade parameters...';
    }

    // Schedule the trade after the "analysis" period
    tradingInterval = setTimeout(() => {
        if (!isTrading || isPaused) return;

        // Determine trade direction based on "AI analysis"
        const direction = Math.random() < 0.5 ? 'BUY' : 'SELL';

        // Risk level is now handled by the money management system
        // The trade amount is calculated based on the current portfolio and previous results

        // Calculate trade amount using Neural Pulse money management system
        const tradeAmount = calculateNeuralMasanielloStake();

        if (aiStatusText) {
            aiStatusText.textContent = `Executing ${direction.toLowerCase()} signal`;
        }

        if (tradingStatus) {
            tradingStatus.textContent = `AI recommends ${direction} trade`;
        }

        if (nextAction) {
            nextAction.textContent = `Placing ${direction.toLowerCase()} trade for $${tradeAmount}`;
        }

        if (aiMessage) {
            const messages = [
                'Pattern recognition complete. High-probability setup detected.',
                'Market volatility analysis indicates favorable conditions for this trade.',
                'Price action and momentum indicators align for this trading opportunity.',
                'Neural network has identified a strong directional bias in current market conditions.',
                'Multiple technical factors confirm this trading signal.'
            ];
            aiMessage.textContent = messages[Math.floor(Math.random() * messages.length)];
        }

        // Place the trade
        placeTrade(direction);

        // Note: Next trading cycle will be scheduled after trade result is received

    }, analysisTime);
}

/**
 * Variables for Quantum Edge mode
 */
let isTrading = false;
let isPaused = false;
let tradingInterval = null;
let currentTradeAmount = 1.01; // Starting trade amount (first value in martingale sequence)
let initialTradeAmount = 1.01; // Base trade amount (first value in martingale sequence)
// Using the existing consecutiveLosses variable
let riskCapital = 0;
let targetProfit = 0;
let profitLoss = 0;







/**
 * Update the balance display
 */
function updateBalance() {
    // Ensure the balance is a valid number
    if (isNaN(balance) || balance === null || balance === undefined) {
        console.error('Invalid balance value in updateBalance:', balance);
        return;
    }

    // Log the balance for debugging
    debugLog('Balance updated to:', balance);

    // Balance display updates can be added here if needed
}

/**
 * Highlight the balance element to draw attention to it
 * Note: This function is currently empty as we've removed the balance display
 * from the interface. It will be updated later when we add the balance display back.
 */
function highlightBalance() {
    // This function will be updated later when we add the balance display back
    debugLog('Balance highlight requested (currently disabled)');
}

/**
 * Update trade information display
 * Note: This function is currently simplified as we've removed the trade info display
 * from the interface. It will be updated later when we add the trade info display back.
 */
function updateTradeInfo(direction, amount, expiry, startTime, endTime) {
    if (!activeTrade) {
        return;
    }

    // Calculate remaining time for debugging
    const now = new Date();
    const remaining = Math.max(0, Math.round((endTime - now) / 1000));

    // Log trade info for debugging
    debugLog(`Active trade: ${direction} - $${amount?.toFixed(2)} - Time left: ${formatTime(remaining)}`);

    // Update remaining time every second
    if (remaining > 0) {
        setTimeout(() => {
            if (activeTrade) {
                updateTradeInfo(direction, amount, expiry, startTime, endTime);
            }
        }, 1000);
    }
}

function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}m ${secs}s`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}

/**
 * Update statistics display
 * Note: This function is currently simplified as we've removed the statistics display
 * from the interface. It will be updated later when we add the statistics display back.
 */
function updateStatistics() {
    // Calculate win rate for debugging
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

    // Calculate profit/loss percentage for debugging
    const profitLossPercentage = ((balance - initialBalance) / initialBalance) * 100;

    // Log statistics for debugging
    debugLog(`Statistics: Win rate: ${winRate.toFixed(1)}%, P/L: ${profitLossPercentage.toFixed(1)}%, Total trades: ${totalTrades}`);
}

function showNotification(message, type) {
    // Send message to content script to show notification
    window.parent.postMessage({
        action: 'showNotification',
        message,
        type
    }, '*');
}

/**
 * Show a congratulations popup when profit target is reached
 */
function showCongratulationsPopup(profit) {
    // Create a popup overlay
    const popupOverlay = document.createElement('div');
    popupOverlay.className = 'popup-overlay';
    popupOverlay.style.position = 'fixed';
    popupOverlay.style.top = '0';
    popupOverlay.style.left = '0';
    popupOverlay.style.width = '100%';
    popupOverlay.style.height = '100%';
    popupOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    popupOverlay.style.zIndex = '10000000';
    popupOverlay.style.display = 'flex';
    popupOverlay.style.justifyContent = 'center';
    popupOverlay.style.alignItems = 'center';

    // Create the popup content
    const popupContent = document.createElement('div');
    popupContent.className = 'popup-content';
    popupContent.style.backgroundColor = 'var(--medium-bg)';
    popupContent.style.borderRadius = '10px';
    popupContent.style.padding = '20px';
    popupContent.style.boxShadow = '0 5px 20px rgba(0, 0, 0, 0.5)';
    popupContent.style.textAlign = 'center';
    popupContent.style.maxWidth = '80%';
    popupContent.style.border = '2px solid var(--success-color)';

    // Add congratulations message
    const title = document.createElement('h2');
    title.textContent = 'Congratulations!';
    title.style.color = 'var(--success-color)';
    title.style.marginBottom = '15px';

    const message = document.createElement('p');
    message.innerHTML = `You've reached your profit target!<br>Total profit: <strong>$${profit.toFixed(2)}</strong>`;
    message.style.color = 'var(--text-light)';
    message.style.marginBottom = '20px';

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.padding = '8px 16px';
    closeButton.style.backgroundColor = 'var(--primary-color)';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '5px';
    closeButton.style.cursor = 'pointer';

    // Add event listener to close the popup
    closeButton.addEventListener('click', () => {
        document.body.removeChild(popupOverlay);
    });

    // Assemble the popup
    popupContent.appendChild(title);
    popupContent.appendChild(message);
    popupContent.appendChild(closeButton);
    popupOverlay.appendChild(popupContent);

    // Add the popup to the document
    document.body.appendChild(popupOverlay);

    // Also send a message to the parent to show the popup there
    window.parent.postMessage({
        action: 'showCongratulationsPopup',
        profit: profit
    }, '*');
}

function saveSettings() {
    // Send message to content script to save settings
    window.parent.postMessage({
        action: 'saveSettings',
        settings: {
            balance,
            initialBalance,
            riskPerTrade,
            currentMode,
            isAutoTrading,
            totalTrades,
            winningTrades,
            losingTrades,
            consecutiveWins,
            consecutiveLosses,
            realBalanceDetected: true, // Flag to indicate we're using real balance
            riskCapital,
            targetProfit,
            profitLoss
        }
    }, '*');
}

// Listen for messages from the content script or iframe
window.addEventListener('message', function(event) {
    try {
        const message = event.data;

        // Skip if message is null or not an object
        if (!message || typeof message !== 'object') {
            return;
        }

        debugLog('Floating interface received message:', message);

        // Handle messages from Neural Pulse iframe
        if (message.action === 'executeTrade' && message.mode === 'neural') {
            debugLog('Received trade execution request from Neural Pulse:', message);

            // Forward the trade execution request to the content script
            window.parent.postMessage({
                action: 'executeTrade',
                direction: message.direction,
                amount: message.amount,
                expiry: message.expiry || 5, // Default to 5 seconds if not specified
                mode: 'neural'
            }, '*');

            return;
        }

        // Handle notification requests from Neural Pulse iframe
        if (message.action === 'showNotification') {
            debugLog('Received notification request from Neural Pulse:', message);

            // Forward the notification request to the content script
            window.parent.postMessage({
                action: 'showNotification',
                message: message.message,
                type: message.type || 'info'
            }, '*');

            return;
        }

        // Handle settings save requests from Neural Pulse iframe
        if (message.action === 'saveSettings' && message.settings) {
            debugLog('Received settings save request from Neural Pulse:', message);

            // Save the settings
            try {
                const settings = { ...loadSettings(), ...message.settings };
                localStorage.setItem('neuralPulseSettings', JSON.stringify(settings));
            } catch (e) {
                console.error('Failed to save Neural Pulse settings:', e);
            }

            return;
        }

        // Ignore any balance updates with value 1000 (default value)
        if (message.action === 'updateBalance' && message.balance === 1000) {
            debugLog('Ignoring default balance update of 1000');
            return;
        }

        if (message.action === 'updateBalance') {
            if (message.realBalance) {
                // This is a real balance update from the website
                debugLog('Received real balance update:', message.balance, 'Original value:', message.originalBalance);

                // Ensure the balance is a valid number
                let numericBalance = message.balance;
                if (typeof numericBalance === 'string') {
                    // Remove any commas and convert to number
                    numericBalance = parseFloat(numericBalance.replace(/,/g, ''));
                }

                // Validate the balance is a number
                if (isNaN(numericBalance)) {
                    console.error('Invalid balance value received:', message.balance);
                    return;
                }

                // Set the realBalanceReceived flag to true
                realBalanceReceived = true;

                // Check if this is the first time we're getting a balance
                const isFirstBalance = !localStorage.getItem('pocketOptionRealBalance');

                // Update the balance
                balance = numericBalance;

                // Only update initial balance if this is the first time we're getting a real balance
                if (isFirstBalance) {
                    initialBalance = numericBalance;
                    debugLog('First time getting real balance:', numericBalance);

                    // Don't show notification about the real balance
                    // showNotification(`Real balance detected: $${numericBalance.toFixed(2)}`, 'success');

                    // Highlight the balance element
                    highlightBalance();
                }

                // Update the UI
                updateBalance();

                // Store the real balance in localStorage
                try {
                    localStorage.setItem('pocketOptionRealBalance', numericBalance.toString());
                    localStorage.setItem('pocketOptionRealBalanceTimestamp', Date.now().toString());
                } catch (e) {
                    console.error('Failed to save real balance to localStorage:', e);
                }
            } else {
                // This is a simulated balance update from trading
                // Only accept simulated updates if we haven't received a real balance yet
                if (!realBalanceReceived) {
                    balance = message.balance;
                    updateBalance();
                } else {
                    debugLog('Ignoring simulated balance update because real balance has been received');
                }
            }
        } else if (message.action === 'highlightBalance') {
            // Highlight the balance element
            highlightBalance();
        } else if (message.action === 'tradeExecuted') {
            // Handle trade execution result from content script
            debugLog('Received trade execution result:', message);

            if (message.success) {
                showNotification(`${message.direction} trade executed successfully for $${message.amount}`, 'success');

                // Update UI to show trade is in progress
                const tradingStatus = document.getElementById('tradingStatus');
                if (tradingStatus) {
                    tradingStatus.textContent = `${message.direction} trade executed for $${message.amount}, waiting for result...`;
                }
            } else {
                showNotification(`Failed to execute trade: ${message.error || 'Unknown error'}`, 'error');

                // Reset active trade flag
                activeTrade = false;

                // Update UI to show trade failed
                const tradingStatus = document.getElementById('tradingStatus');
                if (tradingStatus) {
                    tradingStatus.textContent = `Failed to execute trade: ${message.error || 'Unknown error'}`;
                }
            }
        } else if (message.action === 'tradeResult') {
            debugLog('Received trade result:', message.result, 'Trade ID:', message.tradeId);

            // Make sure to reset the activeTrade flag
            activeTrade = false;

            // Log the state before and after resetting the flag
            debugLog('Before handling trade result - activeTrade:', activeTrade);

            // Handle the trade result based on current mode
            if (currentMode === 'neural') {
                // Handle Neural Pulse trade result directly
                handleTradeResult(message.result);

                // Schedule the next Neural Pulse trading cycle
                if (isTrading && !isPaused) {
                    debugLog('Scheduling next Neural Pulse trade after result');
                    setTimeout(() => {
                        if (isTrading && !isPaused && currentMode === 'neural') {
                            startNeuralTradingCycle();
                        }
                    }, 10000); // 10 seconds delay
                }

            } else if (currentMode === 'quantum') {
                // Handle quantum mode trade result
                handleTradeResult(message.result);
            } else {
                // Handle the trade result in this window
                handleTradeResult(message.result);
            }

            // Log that we've reset the active trade flag
            debugLog('After handling trade result - activeTrade:', activeTrade);
        } else if (message.action === 'resetTrade') {
            debugLog('Received reset trade command. Trade ID:', message.tradeId);

            // Reset the active trade flag
            if (activeTrade) {
                debugLog('Resetting activeTrade flag from true to false');
                activeTrade = false;
                currentTrade = null;

                // Update UI
                const tradingStatus = document.getElementById('tradingStatus');
                if (tradingStatus) {
                    tradingStatus.textContent = 'Trade reset, ready for next trade';
                }

                // Force a new trade cycle if we're in auto trading mode
                if (isTrading && !isPaused) {
                    debugLog('Scheduling next trade after reset');
                    // Use a random delay between 3-8 seconds
                    const nextTradeDelay = Math.floor(Math.random() * 5000) + 3000;
                    setTimeout(startTradingCycle, nextTradeDelay);
                }
            } else {
                debugLog('activeTrade flag already false, no need to reset');
            }
        } else if (message.action === 'loadSettings') {
            const settings = message.settings;
            debugLog('Received settings:', settings);

            // Only use saved balance if we don't have a real balance from the site
            // and it's not the default value of 1000
            if (!settings.realBalanceDetected) {
                if (settings.balance !== 1000) {
                    balance = settings.balance || balance;
                }
                if (settings.initialBalance !== 1000) {
                    initialBalance = settings.initialBalance || initialBalance;
                }
            }

            riskPerTrade = settings.riskPerTrade || riskPerTrade;
            currentMode = settings.currentMode || currentMode;
            totalTrades = settings.totalTrades || 0;
            winningTrades = settings.winningTrades || 0;
            losingTrades = settings.losingTrades || 0;
            consecutiveWins = settings.consecutiveWins || 0;
            consecutiveLosses = settings.consecutiveLosses || 0;

            // Load Quantum Edge specific settings
            if (settings.riskCapital) riskCapital = settings.riskCapital;
            if (settings.targetProfit) targetProfit = settings.targetProfit;
            if (settings.profitLoss) profitLoss = settings.profitLoss;
            if (settings.currentTradeAmount) currentTradeAmount = settings.currentTradeAmount;
            if (settings.initialTradeAmount) initialTradeAmount = settings.initialTradeAmount;

            debugLog('Loaded settings - riskCapital:', riskCapital, 'targetProfit:', targetProfit, 'profitLoss:', profitLoss);

            // Update UI
            updateBalance();
            setMode(currentMode);
        } else if (message.action === 'phoenixSystemReady') {
            // Phoenix System iframe is ready
            debugLog('Phoenix System iframe is ready');

            // Send the current balance to the Phoenix System iframe
            const phoenixFrame = document.getElementById('phoenixSystemFrame');
            if (phoenixFrame) {
                phoenixFrame.contentWindow.postMessage({
                    action: 'updateBalance',
                    balance: balance
                }, '*');
            }
        } else if (message.action === 'resizeIframe') {
            // Resize the iframe based on content height
            const phoenixFrame = document.getElementById('phoenixSystemFrame');
            if (phoenixFrame && message.height) {
                // Add a small buffer to ensure all content is visible
                // Ensure a minimum height of 1200px
                const newHeight = Math.max(message.height + 50, 1200);
                debugLog('Resizing Phoenix iframe to height:', newHeight);
                phoenixFrame.style.height = newHeight + 'px';

                // Force the floating container to update its scroll area
                const floatingContainer = document.getElementById('floatingContainer');
                if (floatingContainer) {
                    floatingContainer.style.height = floatingContainer.style.height;
                }
            }
        } else if (message.action === 'phoenixTradeResult') {
            // Handle trade result from Phoenix System
            debugLog('Received trade result from Phoenix System:', message);

            // Update the UI with the trade result
            showNotification(`Phoenix System: ${message.result.toUpperCase()} trade for $${message.amount.toFixed(2)}`,
                message.result === 'win' ? 'success' : 'error');

            // Update profit/loss tracking
            profitLoss = message.profitLoss;

            // Check if target profit reached
            if (message.profitLoss >= targetProfit) {
                showNotification(`🎉 Congratulations! You've reached your profit target of $${targetProfit.toFixed(2)}!`, 'success');
            }

            // Save settings
            saveSettings();
        } else if (message.action === 'initialize') {
            // Handle initialization message
            debugLog('Received initialization message');

            // If we have settings, load them
            if (message.settings) {
                const settings = message.settings;

                // Only use saved balance if we don't have a real balance from the site
                // and it's not the default value of 1000
                if (!settings.realBalanceDetected) {
                    if (settings.balance !== 1000) {
                        balance = settings.balance || balance;
                    }
                    if (settings.initialBalance !== 1000) {
                        initialBalance = settings.initialBalance || initialBalance;
                    }
                }

                riskPerTrade = settings.riskPerTrade || riskPerTrade;
                currentMode = settings.currentMode || currentMode;
                totalTrades = settings.totalTrades || 0;
                winningTrades = settings.winningTrades || 0;
                losingTrades = settings.losingTrades || 0;
                consecutiveWins = settings.consecutiveWins || 0;
                consecutiveLosses = settings.consecutiveLosses || 0;

                // Load Quantum Edge specific settings
                if (settings.riskCapital) riskCapital = settings.riskCapital;
                if (settings.targetProfit) targetProfit = settings.targetProfit;
                if (settings.profitLoss) profitLoss = settings.profitLoss;
                if (settings.currentTradeAmount) currentTradeAmount = settings.currentTradeAmount;
                if (settings.initialTradeAmount) initialTradeAmount = settings.initialTradeAmount;

                debugLog('Initialized with settings - riskCapital:', riskCapital, 'targetProfit:', targetProfit, 'profitLoss:', profitLoss);

                // Update UI
                updateBalance();
                setMode(currentMode);
            }
        }
    } catch (error) {
        console.error('Error processing message:', error);
    }
});

// Initialize the interface when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    debugLog('DOM content loaded, initializing interface');
    initializeInterface();
    loadSettings();
    loadNeuralPulseSettings();
});

// Also initialize on window load to ensure everything is fully loaded
window.onload = function() {
    debugLog('Window loaded, ensuring interface is initialized');

    // Double-check that mode-specific event listeners are set up
    setupModeEventListeners();

    // Log the current state
    debugLog('Current state after window load:', {
        currentMode,
        isTrading,
        isPaused,
        activeTrade,
        balance,
        currentTradeAmount
    });

    // Send a message to the parent window to confirm the interface is ready
    window.parent.postMessage({
        action: 'floatingInterfaceReady'
    }, '*');
};

/**
 * Quantum Trading Bot Class - Advanced Adaptive System
 * Based on the KOS Adaptive Signal Bot with quantum enhancements
 */
class QuantumTradingBot {
    constructor() {
        this.balance = 0;
        this.initialBalance = 0;
        this.isRunning = false;
        this.isPaused = false;
        this.waitingForResult = false;
        this.consecutiveLosses = 0;
        this.consecutiveWins = 0;
        this.payoutPercentage = 85;
        this.riskPercentage = 1;
        this.winRate = 0.75; // 75% win rate for quantum mode
        this.aggressiveThreshold = 0.35;
        this.maxRiskMultiplier = 3.8;
        this.winStreakMultiplier = 2.8;
        this.recoveryMultiplier = 2.2;
        this.baseRiskMultiplier = 1.5;
        this.drawdownProtection = 0.75;
        this.martingaleBase = 2.5;
        this.totalTrades = 0;
        this.wins = 0;
        this.losses = 0;
        this.totalProfit = 0;
        this.totalLoss = 0;
        this.maxDrawdown = 0;
        this.peakBalance = 0;
        this.trendDirection = null;
        this.lastTradeAmount = null;
        this.winStreak = 0;
        this.profitTarget = 0;
        this.lastTradeDirection = null;
        this.loadingShown = false;
        this.tradingInterval = null;
        this.currentSignal = null;
    }

    setBalance(amount) {
        this.balance = parseFloat(amount);
        this.initialBalance = this.balance;
        this.peakBalance = this.balance;
        this.updateBalance();
        this.updateStats();
    }

    setPayoutPercentage(percentage) {
        this.payoutPercentage = parseFloat(percentage);
    }

    setRiskPercentage(percentage) {
        this.riskPercentage = parseFloat(percentage);
    }

    setProfitTarget(target) {
        this.profitTarget = parseFloat(target) || 0;
    }

    start() {
        if (this.isRunning || this.balance <= 0) return;
        this.isRunning = true;
        this.isPaused = false;
        this.generateSignal();
    }

    stop() {
        this.isRunning = false;
        this.isPaused = false;
        this.waitingForResult = false;
        this.loadingShown = false;
        this.currentSignal = null;

        // Hide signal display and result buttons
        this.hideSignalDisplay();

        if (this.tradingInterval) {
            clearTimeout(this.tradingInterval);
            this.tradingInterval = null;
        }
    }

    pause() {
        this.isPaused = true;
        if (this.tradingInterval) {
            clearTimeout(this.tradingInterval);
            this.tradingInterval = null;
        }
    }

    resume() {
        this.isPaused = false;
        if (this.isRunning && !this.waitingForResult) {
            this.generateSignal();
        }
    }

    async generateSignal() {
        if (!this.isRunning || this.isPaused || this.waitingForResult) return;

        // Show loading animation only once
        if (!this.loadingShown) {
            this.showQuantumAnalysis();
            this.loadingShown = true;
            // Wait for analysis to complete
            await new Promise(resolve => setTimeout(resolve, 3000));
        }

        const direction = this.determineTradeDirection();
        const expirationTime = this.determineExpirationTime();
        const tradeAmount = this.calculateTradeAmount();
        const confidence = 75 + Math.random() * 20; // 75-95% confidence

        this.currentSignal = {
            direction,
            expirationTime,
            amount: tradeAmount,
            confidence,
            timestamp: new Date()
        };

        this.lastTradeAmount = tradeAmount;
        this.waitingForResult = true;

        debugLog('Quantum Bot generated signal:', {
            direction,
            amount: tradeAmount,
            expiry: expirationTime,
            confidence: confidence.toFixed(0) + '%',
            mode: 'quantum'
        });

        // Update status
        const statusElement = document.getElementById('quantumTradingStatus');
        if (statusElement) {
            statusElement.textContent = `Signal generated: ${direction} - $${tradeAmount.toFixed(2)}`;
        }

        // Show signal display
        this.showSignalDisplay(direction, confidence, expirationTime, tradeAmount);
    }

    showQuantumAnalysis() {
        const statusElement = document.getElementById('quantumTradingStatus');
        if (statusElement) {
            const steps = [
                'Initializing Quantum Field...',
                'Analyzing market quantum states...',
                'Calibrating adaptive algorithms...',
                'Optimizing position sizing...',
                'Quantum system ready...'
            ];

            let stepIndex = 0;
            const showStep = () => {
                if (stepIndex < steps.length) {
                    statusElement.textContent = steps[stepIndex];
                    stepIndex++;
                    setTimeout(showStep, 600);
                } else {
                    statusElement.textContent = 'Ready to generate signals...';
                }
            };
            showStep();
        }
    }

    showSignalDisplay(direction, confidence, expirationTime, tradeAmount) {
        // Update signal display elements
        const signalDirectionElement = document.getElementById('quantumSignalDirection');
        const signalStrengthElement = document.getElementById('quantumSignalStrength');
        const signalExpiryElement = document.getElementById('quantumSignalExpiry');
        const signalDisplay = document.getElementById('quantumSignalDisplay');
        const resultButtons = document.getElementById('quantumResultButtons');
        const currentTradeAmountElement = document.getElementById('quantumCurrentTradeAmount');

        if (signalDirectionElement) {
            signalDirectionElement.textContent = direction;
        }
        if (signalStrengthElement) {
            signalStrengthElement.textContent = `${confidence.toFixed(0)}%`;
        }
        if (signalExpiryElement) {
            signalExpiryElement.textContent = `${expirationTime} min`;
        }
        if (currentTradeAmountElement) {
            currentTradeAmountElement.textContent = `Signal: $${tradeAmount.toFixed(2)} ${direction}`;
        }

        // Show signal display and result buttons
        if (signalDisplay) {
            signalDisplay.style.display = 'block';
        }
        if (resultButtons) {
            resultButtons.style.display = 'block';
        }
    }

    hideSignalDisplay() {
        const signalDisplay = document.getElementById('quantumSignalDisplay');
        const resultButtons = document.getElementById('quantumResultButtons');

        if (signalDisplay) {
            signalDisplay.style.display = 'none';
        }
        if (resultButtons) {
            resultButtons.style.display = 'none';
        }
    }

    handleManualResult(resultType) {
        if (!this.waitingForResult || !this.currentSignal) return;

        const isWin = resultType === 'W';
        const tradeAmount = this.lastTradeAmount;

        debugLog('Quantum Bot handling manual result:', resultType, 'Amount:', tradeAmount);

        // Hide signal display
        this.hideSignalDisplay();

        // Reset waiting state
        this.waitingForResult = false;
        this.currentSignal = null;

        // Process the result
        this.handleTradeResult(isWin ? 'WIN' : 'LOSS');
    }

    calculateTradeAmount() {
        let baseAmount = this.balance * (this.riskPercentage / 100) * this.baseRiskMultiplier;

        if (this.consecutiveLosses > 0) {
            const recoveryFactor = Math.pow(this.martingaleBase, Math.min(this.consecutiveLosses, 5));
            baseAmount *= Math.min(recoveryFactor, this.maxRiskMultiplier);
        }

        if (this.consecutiveWins >= 2) {
            baseAmount *= this.winStreakMultiplier;
            if (this.consecutiveWins >= 3) {
                baseAmount *= 1.5;
            }
        }

        if (this.balance < this.peakBalance * this.drawdownProtection) {
            baseAmount *= 0.6;
        }

        if (this.totalTrades > 20 && (this.wins / this.totalTrades) > 0.35) {
            baseAmount *= 1.3;
        }

        return Math.min(baseAmount, this.balance * 0.15);
    }

    determineTradeDirection() {
        const winRate = this.wins / Math.max(this.totalTrades, 1);
        if (this.lastTradeDirection === 'BUY') {
            return Math.random() > 0.4 ? 'SELL' : 'BUY';
        } else if (this.lastTradeDirection === 'SELL') {
            return Math.random() > 0.4 ? 'BUY' : 'SELL';
        }
        return Math.random() > 0.5 ? 'BUY' : 'SELL';
    }

    determineExpirationTime() {
        if (this.consecutiveWins > 2) {
            return 1;
        } else if (this.consecutiveLosses > 1) {
            return 3;
        }
        return Math.floor(Math.random() * 2) + 1;
    }

    recordTrade(direction, expirationTime, amount) {
        this.lastTradeAmount = amount;

        // Update current trade amount display
        const currentTradeAmountElement = document.getElementById('quantumCurrentTradeAmount');
        if (currentTradeAmountElement) {
            currentTradeAmountElement.textContent = `Current trade: $${amount.toFixed(2)} ${direction}`;
        }

        // Update status
        const statusElement = document.getElementById('quantumTradingStatus');
        if (statusElement) {
            statusElement.textContent = `Executing ${direction} trade - $${amount.toFixed(2)} (${expirationTime}min)`;
        }
    }

    handleTradeResult(result) {
        this.waitingForResult = false;
        const tradeAmount = this.lastTradeAmount || this.calculateTradeAmount();

        debugLog('Quantum Bot handling trade result:', result, 'Amount:', tradeAmount);

        if (result === 'WIN' || result === 'W') {
            const profit = tradeAmount * (this.payoutPercentage / 100);
            this.balance += profit;
            this.totalProfit += profit;
            this.consecutiveWins++;
            this.consecutiveLosses = 0;
            this.wins++;
            if (this.balance > this.peakBalance) {
                this.peakBalance = this.balance;
            }
            this.lastTradeDirection = 'BUY';
        } else {
            this.balance -= tradeAmount;
            this.totalLoss += tradeAmount;
            this.consecutiveLosses++;
            this.consecutiveWins = 0;
            this.losses++;
            const drawdown = this.peakBalance > 0 ? (this.peakBalance - this.balance) / this.peakBalance : 0;
            if (drawdown > this.maxDrawdown) {
                this.maxDrawdown = drawdown;
            }
            this.lastTradeDirection = 'SELL';
        }

        this.totalTrades++;
        this.updateTrendDirection();
        this.updateBalance();
        this.updateStats();

        // Check if profit target has been reached
        if (this.profitTarget > 0) {
            const currentProfit = this.balance - this.initialBalance;
            if (currentProfit >= this.profitTarget) {
                this.stop();
                this.showProfitTargetReached(currentProfit);
                return;
            }
        }

        if (this.balance <= 0) {
            this.stop();
            const statusElement = document.getElementById('quantumTradingStatus');
            if (statusElement) {
                statusElement.textContent = "Quantum system stopped - Insufficient balance";
            }
            return;
        }

        // Continue trading after a delay
        if (this.isRunning && !this.isPaused && !this.waitingForResult) {
            this.tradingInterval = setTimeout(() => {
                this.generateSignal();
            }, 5000); // 5 second delay between trades
        }
    }

    updateTrendDirection() {
        const winRate = this.wins / Math.max(this.totalTrades, 1);
        if (winRate > 0.55) {
            this.trendDirection = 'UP';
        } else if (winRate < 0.45) {
            this.trendDirection = 'DOWN';
        } else {
            this.trendDirection = null;
        }
    }

    updateBalance() {
        // Update balance display in quantum mode
        const balanceElement = document.querySelector('.quantum-mode .balance-value');
        if (balanceElement) {
            balanceElement.textContent = `$${this.balance.toFixed(2)}`;
        }
    }

    updateStats() {
        const winRate = this.wins / Math.max(this.totalTrades, 1) * 100;
        const profitFactor = this.totalLoss ? this.totalProfit / this.totalLoss : 0;

        const winRateElement = document.getElementById('quantumWinRate');
        const profitFactorElement = document.getElementById('quantumProfitFactor');
        const totalTradesElement = document.getElementById('quantumTotalTrades');

        if (winRateElement) winRateElement.textContent = `${winRate.toFixed(1)}%`;
        if (profitFactorElement) profitFactorElement.textContent = profitFactor.toFixed(2);
        if (totalTradesElement) totalTradesElement.textContent = this.totalTrades;
    }

    showProfitTargetReached(profit) {
        // Show congratulations message
        showNotification(`🎯 Profit target reached! Made $${profit.toFixed(2)}`, 'success');

        const statusElement = document.getElementById('quantumTradingStatus');
        if (statusElement) {
            statusElement.textContent = `🎯 Target achieved! Profit: $${profit.toFixed(2)}`;
        }
    }
}
