/**
 * KOS Quantum Edge Bot
 * Advanced adaptive position sizing with anti-martingale progression
 */

// Global variables
let isActive = false;
let isPaused = false;
let waitingForResult = false;
let currentBalance = 0;
let initialBalance = 0;
let riskCapital = 50; // Percentage
let profitTarget = 10; // Percentage
let quantumMultiplier = 2.087;
let maxSequence = 5;
let currentSequence = 0;
let consecutiveLosses = 0;
let totalTrades = 0;
let winningTrades = 0;
let losingTrades = 0;
let trades = [];
let tradingInterval;
let currentSignal = null;
let lastTradeAmount = 0;

// DOM elements
const activateBtn = document.getElementById('activateBtn');
const pauseBtn = document.getElementById('pauseBtn');
const riskCapitalInput = document.getElementById('riskCapital');
const profitTargetInput = document.getElementById('profitTarget');
const quantumMultiplierInput = document.getElementById('quantumMultiplier');
const maxSequenceInput = document.getElementById('maxSequence');

// Status elements
const currentStateElement = document.getElementById('currentState');
const nextActionElement = document.getElementById('nextAction');
const tradeAmountElement = document.getElementById('tradeAmount');

// Stats elements
const totalTradesElement = document.getElementById('totalTrades');
const winRateElement = document.getElementById('winRate');
const currentProfitElement = document.getElementById('currentProfit');
const currentBalanceElement = document.getElementById('currentBalance');

// Trades list
const tradesListElement = document.getElementById('tradesList');

// Quantum metrics
const coherenceElement = document.getElementById('coherenceValue');
const entanglementElement = document.getElementById('entanglementValue');
const probabilityElement = document.getElementById('probabilityValue');

// Signal display elements
const signalDisplayElement = document.getElementById('signalDisplay');
const signalDirectionElement = document.getElementById('signalDirectionValue');
const signalConfidenceElement = document.getElementById('signalConfidenceValue');
const signalExpiryElement = document.getElementById('signalExpiryValue');

// Result buttons
const resultButtonsElement = document.getElementById('resultButtons');
const winButton = document.getElementById('winButton');
const loseButton = document.getElementById('loseButton');

// Initialize the interface
function initializeInterface() {
    console.log('Initializing Quantum Edge Bot interface');

    // Set up event listeners
    activateBtn.addEventListener('click', toggleActivation);
    pauseBtn.addEventListener('click', togglePause);

    // Configuration inputs
    riskCapitalInput.addEventListener('change', updateRiskCapital);
    profitTargetInput.addEventListener('change', updateProfitTarget);
    quantumMultiplierInput.addEventListener('change', updateQuantumMultiplier);
    maxSequenceInput.addEventListener('change', updateMaxSequence);

    // Win/Loss buttons
    winButton.addEventListener('click', () => handleManualResult('W'));
    loseButton.addEventListener('click', () => handleManualResult('L'));

    // Initialize quantum metrics animation
    animateQuantumMetrics();

    // Update initial state
    updateStatus();
    updateStats();

    // Listen for messages from parent
    window.addEventListener('message', handleMessage);

    console.log('Quantum Edge Bot interface initialized');
}

// Handle messages from parent window
function handleMessage(event) {
    const { action, data } = event.data;

    switch (action) {
        case 'updateBalance':
            currentBalance = data.balance;
            if (initialBalance === 0) {
                initialBalance = currentBalance;
            }
            updateStats();
            break;

        case 'tradeResult':
            handleTradeResult(data);
            break;

        case 'forceHeightUpdate':
            // Force height update if needed
            break;

        default:
            console.log('Unknown message action:', action);
    }
}

// Toggle activation
function toggleActivation() {
    isActive = !isActive;

    if (isActive) {
        startTrading();
    } else {
        stopTrading();
    }

    updateButtons();
    updateStatus();
}

// Toggle pause
function togglePause() {
    isPaused = !isPaused;
    updateButtons();
    updateStatus();

    if (isPaused) {
        clearInterval(tradingInterval);
    } else if (isActive) {
        startTradingCycle();
    }
}

// Start trading
function startTrading() {
    console.log('Starting Quantum Edge trading');
    currentSequence = 0;
    consecutiveLosses = 0;
    startTradingCycle();
}

// Stop trading
function stopTrading() {
    console.log('Stopping Quantum Edge trading');
    clearInterval(tradingInterval);

    // Reset manual trading state
    waitingForResult = false;
    currentSignal = null;

    // Hide signal display and result buttons
    signalDisplayElement.style.display = 'none';
    resultButtonsElement.style.display = 'none';
}

// Start trading cycle
function startTradingCycle() {
    if (!isActive || isPaused || waitingForResult) return;

    // Calculate next trade amount
    const tradeAmount = calculateAdaptivePosition();
    lastTradeAmount = tradeAmount;

    // Update UI
    updateTradeAmount(tradeAmount);

    // Simulate quantum analysis delay
    setTimeout(() => {
        if (isActive && !isPaused && !waitingForResult) {
            generateQuantumSignal(tradeAmount);
        }
    }, 2000 + Math.random() * 3000); // 2-5 second delay
}

// Calculate adaptive position size
function calculateAdaptivePosition() {
    const riskAmount = (currentBalance * riskCapital) / 100;
    
    if (consecutiveLosses === 0) {
        // Base position
        return Math.round((riskAmount * 0.02) * 100) / 100; // 2% of risk capital
    } else {
        // Adaptive progression
        const multiplier = Math.pow(quantumMultiplier, consecutiveLosses);
        const adaptiveAmount = (riskAmount * 0.02) * multiplier;
        
        // Cap at maximum sequence
        if (consecutiveLosses >= maxSequence) {
            return Math.round((riskAmount * 0.1) * 100) / 100; // 10% of risk capital max
        }
        
        return Math.round(adaptiveAmount * 100) / 100;
    }
}

// Generate quantum signal
function generateQuantumSignal(amount) {
    const direction = Math.random() > 0.5 ? 'CALL' : 'PUT';
    const confidence = 75 + Math.random() * 20; // 75-95% confidence
    const expiryMinutes = Math.floor(Math.random() * 4) + 2; // 2-5 minutes

    currentSignal = {
        direction: direction,
        amount: amount,
        confidence: confidence,
        expiry: expiryMinutes,
        timestamp: new Date()
    };

    console.log(`Generated quantum signal: ${direction} for $${amount}`);

    // Update status
    currentStateElement.textContent = `Signal generated: ${direction}`;
    nextActionElement.textContent = `Waiting for manual trade execution`;

    // Show signal display
    signalDirectionElement.textContent = direction;
    signalConfidenceElement.textContent = `${confidence.toFixed(0)}%`;
    signalExpiryElement.textContent = `${expiryMinutes} minutes`;

    signalDisplayElement.style.display = 'block';
    resultButtonsElement.style.display = 'block';

    waitingForResult = true;
}

// Handle manual result input
function handleManualResult(resultType) {
    if (!waitingForResult || !currentSignal) return;

    const isWin = resultType === 'W';
    const amount = lastTradeAmount;

    const result = {
        direction: currentSignal.direction,
        amount: amount,
        result: isWin ? 'win' : 'loss',
        profit: isWin ? amount * 0.85 : -amount, // 85% payout
        timestamp: new Date()
    };

    // Hide signal display and result buttons
    signalDisplayElement.style.display = 'none';
    resultButtonsElement.style.display = 'none';

    // Reset waiting state
    waitingForResult = false;
    currentSignal = null;

    handleTradeResult(result);
}

// Handle trade result
function handleTradeResult(result) {
    console.log('Trade result:', result);
    
    totalTrades++;
    
    if (result.result === 'win') {
        winningTrades++;
        consecutiveLosses = 0; // Reset on win
        currentBalance += result.profit;
    } else {
        losingTrades++;
        consecutiveLosses++;
        currentBalance += result.profit; // Profit is negative for losses
    }
    
    // Add to trades history
    trades.unshift(result);
    if (trades.length > 10) {
        trades.pop(); // Keep only last 10 trades
    }
    
    // Update UI
    updateStats();
    updateTradesHistory();
    
    // Check profit target
    const currentProfit = currentBalance - initialBalance;
    const profitPercentage = (currentProfit / initialBalance) * 100;
    
    if (profitPercentage >= profitTarget) {
        showCongratulations(currentProfit);
        stopTrading();
        return;
    }
    
    // Check if we should continue trading
    if (isActive && !isPaused && !waitingForResult) {
        if (consecutiveLosses < maxSequence) {
            currentSequence++;
            setTimeout(startTradingCycle, 3000); // 3 second delay between trades
        } else {
            // Reset sequence after max losses
            consecutiveLosses = 0;
            currentSequence = 0;
            setTimeout(startTradingCycle, 5000); // 5 second delay after reset
        }
    }
}

// Update configuration
function updateRiskCapital() {
    riskCapital = parseFloat(riskCapitalInput.value);
}

function updateProfitTarget() {
    profitTarget = parseFloat(profitTargetInput.value);
}

function updateQuantumMultiplier() {
    quantumMultiplier = parseFloat(quantumMultiplierInput.value);
}

function updateMaxSequence() {
    maxSequence = parseInt(maxSequenceInput.value);
}

// Update UI functions
function updateButtons() {
    if (isActive) {
        activateBtn.innerHTML = '<i class="fas fa-stop"></i><span>Deactivate</span>';
        activateBtn.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
        pauseBtn.disabled = false;
    } else {
        activateBtn.innerHTML = '<i class="fas fa-play"></i><span>Activate Quantum Field</span>';
        activateBtn.style.background = 'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)';
        pauseBtn.disabled = true;
    }
    
    if (isPaused) {
        pauseBtn.innerHTML = '<i class="fas fa-play"></i><span>Resume</span>';
    } else {
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i><span>Pause</span>';
    }
}

function updateStatus() {
    if (!isActive) {
        currentStateElement.textContent = 'Quantum field ready';
        nextActionElement.textContent = 'Awaiting activation';
    } else if (isPaused) {
        currentStateElement.textContent = 'Trading paused';
        nextActionElement.textContent = 'Click resume to continue';
    } else if (waitingForResult) {
        currentStateElement.textContent = 'Signal active';
        nextActionElement.textContent = 'Waiting for trade result input';
    } else {
        currentStateElement.textContent = 'Quantum field active';
        nextActionElement.textContent = 'Analyzing market patterns';
    }
}

function updateTradeAmount(amount) {
    tradeAmountElement.textContent = `$${amount.toFixed(2)}`;
}

function updateStats() {
    totalTradesElement.textContent = totalTrades;
    
    const winRate = totalTrades > 0 ? ((winningTrades / totalTrades) * 100).toFixed(1) : 0;
    winRateElement.textContent = `${winRate}%`;
    
    const currentProfit = currentBalance - initialBalance;
    currentProfitElement.textContent = `$${currentProfit.toFixed(2)}`;
    currentProfitElement.style.color = currentProfit >= 0 ? '#28a745' : '#dc3545';
    
    currentBalanceElement.textContent = `$${currentBalance.toFixed(2)}`;
}

function updateTradesHistory() {
    if (trades.length === 0) {
        tradesListElement.innerHTML = '<div class="no-trades">No trades executed yet</div>';
        return;
    }
    
    const tradesHTML = trades.map(trade => {
        const time = trade.timestamp.toLocaleTimeString();
        const profitClass = trade.result === 'win' ? 'positive' : 'negative';
        
        return `
            <div class="trade-item ${trade.result}">
                <div class="trade-info">
                    <div class="trade-direction">${trade.direction.toUpperCase()}</div>
                    <div class="trade-time">${time}</div>
                </div>
                <div class="trade-result">
                    <div class="trade-amount">$${trade.amount.toFixed(2)}</div>
                    <div class="trade-profit ${profitClass}">${trade.profit >= 0 ? '+' : ''}$${trade.profit.toFixed(2)}</div>
                </div>
            </div>
        `;
    }).join('');
    
    tradesListElement.innerHTML = tradesHTML;
}

// Animate quantum metrics
function animateQuantumMetrics() {
    setInterval(() => {
        // Simulate quantum fluctuations
        const coherence = 85 + Math.random() * 10;
        const entanglement = 90 + Math.random() * 8;
        const probability = 75 + Math.random() * 15;
        
        coherenceElement.textContent = `${coherence.toFixed(0)}%`;
        entanglementElement.textContent = `${entanglement.toFixed(0)}%`;
        probabilityElement.textContent = `${probability.toFixed(0)}%`;
    }, 2000);
}

// Show congratulations
function showCongratulations(profit) {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    overlay.style.zIndex = '10000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';

    // Create popup content
    const popup = document.createElement('div');
    popup.style.backgroundColor = 'rgba(28, 28, 40, 0.98)';
    popup.style.borderRadius = '25px';
    popup.style.padding = '50px';
    popup.style.textAlign = 'center';
    popup.style.maxWidth = '600px';
    popup.style.width = '90%';
    popup.style.border = '3px solid #4a90e2';
    popup.style.boxShadow = '0 25px 80px rgba(0, 0, 0, 0.7), 0 0 40px rgba(74, 144, 226, 0.4)';

    const profitPercentage = ((profit / initialBalance) * 100);

    popup.innerHTML = `
        <div style="margin-bottom: 30px;">
            <h1 style="color: #4a90e2; margin: 0; font-size: 2.8em; text-shadow: 0 0 30px rgba(74, 144, 226, 0.6);">
                🎯 QUANTUM TARGET ACHIEVED! 🎯
            </h1>
        </div>

        <div style="margin-bottom: 40px;">
            <p style="color: #4a90e2; font-size: 1.2em; margin: 10px 0; font-weight: 500;">
                Your quantum profit target has been successfully achieved!
            </p>
        </div>

        <div style="background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(66, 134, 244, 0.1) 100%);
                   border-radius: 20px; padding: 30px; margin: 25px 0;
                   border: 2px solid rgba(74, 144, 226, 0.3);">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; text-align: center;">
                <div style="padding: 15px;">
                    <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Initial Balance</div>
                    <div style="color: #fff; font-size: 1.6em; font-weight: bold;">$${initialBalance.toFixed(2)}</div>
                </div>
                <div style="padding: 15px;">
                    <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Final Balance</div>
                    <div style="color: #4a90e2; font-size: 1.6em; font-weight: bold;">$${currentBalance.toFixed(2)}</div>
                </div>
                <div style="padding: 15px;">
                    <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Total Profit</div>
                    <div style="color: #4a90e2; font-size: 2.2em; font-weight: bold;
                               text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);">$${profit.toFixed(2)}</div>
                </div>
                <div style="padding: 15px;">
                    <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Profit %</div>
                    <div style="color: #4a90e2; font-size: 2.2em; font-weight: bold;
                               text-shadow: 0 0 20px rgba(74, 144, 226, 0.5);">+${profitPercentage.toFixed(1)}%</div>
                </div>
            </div>
        </div>

        <div style="margin-top: 40px;">
            <button id="backButton" style="background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
                   color: white; border: none; padding: 15px 30px; border-radius: 12px; font-size: 1.2em;
                   font-weight: bold; cursor: pointer; transition: all 0.3s;
                   box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4); text-transform: uppercase;">
                <i class="fas fa-arrow-left"></i> Back
            </button>
        </div>
    `;

    overlay.appendChild(popup);
    document.body.appendChild(overlay);

    // Add event listener for back button
    document.getElementById('backButton').addEventListener('click', () => {
        document.body.removeChild(overlay);

        // Reset bot state
        isActive = false;
        waitingForResult = false;
        currentSignal = null;

        // Hide signal display and result buttons
        signalDisplayElement.style.display = 'none';
        resultButtonsElement.style.display = 'none';

        updateButtons();
        updateStatus();
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeInterface);
