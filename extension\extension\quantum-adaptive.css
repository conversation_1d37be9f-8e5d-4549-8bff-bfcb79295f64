/* Quantum Edge Mode Styles */
body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    overflow-x: hidden;
}

.quantum-container {
    padding: 15px;
    max-width: 100%;
    box-sizing: border-box;
}

/* Quantum Status Panel */
.quantum-status-panel {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(74, 144, 226, 0.3);
    box-shadow: 0 8px 32px rgba(74, 144, 226, 0.1);
}

.quantum-status-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.quantum-icon {
    font-size: 24px;
    color: #4a90e2;
    animation: quantumPulse 2s ease-in-out infinite;
}

@keyframes quantumPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.quantum-status-text {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.quantum-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.metric {
    text-align: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(74, 144, 226, 0.2);
}

.metric-label {
    display: block;
    font-size: 12px;
    color: #b0c4de;
    margin-bottom: 5px;
}

.metric-value {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: #4a90e2;
}

/* Trading Controls */
.trading-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.quantum-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.quantum-btn.primary {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.quantum-btn.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.quantum-btn.secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.quantum-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Configuration Panel */
.config-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.config-group {
    margin-bottom: 20px;
}

.config-group:last-child {
    margin-bottom: 0;
}

.config-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
}

.config-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-input {
    flex: 1;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.config-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.config-unit {
    font-size: 14px;
    color: #b0c4de;
    font-weight: 500;
    min-width: 20px;
}

.config-suggestion {
    font-size: 12px;
    color: #8a9ba8;
    margin-top: 5px;
}

/* Trading Status */
.trading-status {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.status-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-size: 14px;
    color: #b0c4de;
}

.status-value {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
}

/* Statistics Panel */
.stats-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stats-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #4a90e2;
    margin-bottom: 5px;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #b0c4de;
}

/* Trades History */
.trades-history {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.trades-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.trades-list {
    max-height: 200px;
    overflow-y: auto;
}

.no-trades {
    text-align: center;
    color: #8a9ba8;
    font-style: italic;
    padding: 20px;
}

.trade-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border-left: 4px solid;
}

.trade-item.win {
    border-left-color: #28a745;
}

.trade-item.loss {
    border-left-color: #dc3545;
}

.trade-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.trade-direction {
    font-weight: 600;
    font-size: 14px;
}

.trade-time {
    font-size: 12px;
    color: #8a9ba8;
}

.trade-result {
    text-align: right;
}

.trade-amount {
    font-weight: 600;
    font-size: 14px;
}

.trade-profit {
    font-size: 12px;
}

.trade-profit.positive {
    color: #28a745;
}

.trade-profit.negative {
    color: #dc3545;
}

/* Signal Display */
.signal-display {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(40, 167, 69, 0.3);
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
    animation: signalGlow 2s ease-in-out infinite alternate;
}

@keyframes signalGlow {
    0% { box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2); }
    100% { box-shadow: 0 8px 32px rgba(40, 167, 69, 0.4); }
}

.signal-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.signal-header i {
    font-size: 24px;
    animation: signalPulse 1.5s ease-in-out infinite;
}

@keyframes signalPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.signal-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.signal-direction,
.signal-confidence,
.signal-expiry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.signal-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.signal-value {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Result Buttons */
.result-buttons {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
}

.result-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.result-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.result-btn:hover {
    transform: translateY(-2px);
}

.win-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.win-btn:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.lose-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.lose-btn:hover {
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.result-instruction {
    text-align: center;
    font-size: 12px;
    color: #8a9ba8;
    font-style: italic;
    margin-top: 10px;
}
