:root {
    --primary-color: #4286f4;
    --secondary-color: #ff6384;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --dark-bg: #0f0f1a;
    --medium-bg: #1a1a2e;
    --light-bg: #28293d;
    --text-light: #ffffff;
    --text-medium: #d1d5db;
    --text-dark: #9ca3af;
    --border-color: rgba(66, 134, 244, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: transparent;
    color: var(--text-light);
    overflow: hidden;
}

.floating-container {
    position: fixed;
    top: 70px;
    left: 10px; /* Position on left side by default */
    width: 450px; /* Further increased width to make the Trading Bot interface wider */
    background-color: rgba(15, 15, 26, 0.95);
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
    z-index: 9999999;
    backdrop-filter: blur(5px);
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, opacity 0.3s ease;
    overflow: hidden; /* Keep hidden for proper styling */
    max-height: 85vh; /* Increased max-height to accommodate Neural Pulse content */
    display: flex;
    flex-direction: column;
    /* Ensure it can be moved anywhere */
    user-select: none;
    touch-action: none;
    /* Remove any constraints */
    margin: 0;
    padding: 0;
}

.floating-container.dragging {
    transition: none;
    opacity: 0.8;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.7);
}

.floating-container.minimized {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    overflow: hidden;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--medium-bg);
    border-bottom: 1px solid var(--border-color);
    cursor: grab;
    position: relative;
    overflow: hidden;
}

.header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    opacity: 0.8;
}

.header:hover::after {
    opacity: 1;
}

.header h1 {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.header h1 i {
    margin-right: 8px;
}

.controls {
    display: flex;
    gap: 5px;
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.control-btn:hover {
    background-color: var(--light-bg);
    color: var(--text-light);
}

.control-btn.locked {
    color: var(--primary-color);
}

.content {
    padding: 15px;
    overflow-y: auto;
    flex: 1;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--medium-bg);
    max-height: calc(85vh - 50px); /* Ensure content is scrollable within container */
}

.content::-webkit-scrollbar {
    width: 8px;
}

.content::-webkit-scrollbar-track {
    background: var(--medium-bg);
    border-radius: 4px;
}

.content::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: 4px;
}

.content::-webkit-scrollbar-thumb:hover {
    background-color: var(--secondary-color);
}

.minimized-content {
    display: none;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--primary-color);
}

.floating-container.minimized .content {
    display: none;
}

.floating-container.minimized .minimized-content {
    display: flex;
}

.floating-container.minimized .header {
    display: none;
}

.mode-selector {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.mode-btn {
    flex: 1;
    padding: 8px 5px;
    border: 1px solid var(--border-color);
    background-color: var(--medium-bg);
    color: var(--text-light);
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    transition: all 0.2s;
}

.mode-btn i {
    margin-right: 3px;
}

.mode-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.mode-btn:hover {
    transform: translateY(-2px);
}

/* Mode-specific styles */
.mode-content {
    margin-top: 15px;
    padding: 10px;
    background-color: var(--medium-bg);
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

/* Special handling for Neural Pulse mode */
.neural-mode.mode-content {
    padding: 0;
    background: none;
    border: none;
    overflow: visible; /* Allow content to be visible */
}



/* Balance display */
.balance-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--dark-bg);
    border-radius: 5px;
    border: 1px solid rgba(66, 134, 244, 0.3);
}

.balance-label {
    font-weight: bold;
    color: var(--text-medium);
}

.balance-value {
    font-weight: bold;
    color: var(--primary-color);
    font-size: 16px;
}



/* Quantum Edge Mode */
.quantum-mode {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(75, 0, 130, 0.05));
    border-color: rgba(138, 43, 226, 0.3);
    position: relative;
    overflow: hidden;
}

.quantum-mode::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(75, 0, 130, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(147, 0, 211, 0.05) 0%, transparent 50%);
    pointer-events: none;
    animation: quantumField 8s ease-in-out infinite;
}

@keyframes quantumField {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.05); }
}

.quantum-mode .mode-header {
    color: #8A2BE2;
    text-align: center;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
    position: relative;
    z-index: 1;
}

.quantum-mode .balance-display {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(75, 0, 130, 0.1));
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(138, 43, 226, 0.3);
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(138, 43, 226, 0.2);
}

.quantum-mode .balance-label {
    color: var(--text-medium);
    font-size: 14px;
    font-weight: 500;
}

.quantum-mode .balance-value {
    font-weight: bold;
    color: #8A2BE2;
    font-size: 18px;
    text-shadow: 0 0 8px rgba(138, 43, 226, 0.6);
}

.quantum-mode .setting-group {
    margin-bottom: 12px;
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.15), rgba(75, 0, 130, 0.08));
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(138, 43, 226, 0.25);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(5px);
}

.quantum-mode .setting-label {
    font-weight: bold;
    color: #B19CD9;
    margin-bottom: 8px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.quantum-mode .setting-value {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.quantum-mode .suggestion {
    color: var(--text-dark);
    font-size: 11px;
    font-style: italic;
    opacity: 0.8;
}

.quantum-mode .risk-input-container {
    width: 100%;
}

.quantum-mode .setting-input {
    width: 100%;
    padding: 8px 12px;
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.1), rgba(75, 0, 130, 0.05));
    border: 1px solid rgba(138, 43, 226, 0.4);
    color: #8A2BE2;
    border-radius: 6px;
    font-weight: bold;
    text-align: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.quantum-mode .setting-input:focus {
    outline: none;
    border-color: #8A2BE2;
    box-shadow: 0 0 15px rgba(138, 43, 226, 0.4);
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(75, 0, 130, 0.1));
}

.quantum-mode .trading-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.quantum-mode .action-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.quantum-mode .action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.quantum-mode .action-btn:hover::before {
    left: 100%;
}

.quantum-mode #quantumStartStopBtn {
    background: linear-gradient(135deg, #8A2BE2, #9932CC);
    box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
}

.quantum-mode #quantumStartStopBtn.stop {
    background: linear-gradient(135deg, #DC143C, #B22222);
    box-shadow: 0 4px 15px rgba(220, 20, 60, 0.4);
}

.quantum-mode #quantumPauseResumeBtn {
    background: linear-gradient(135deg, #FF8C00, #FF7F50);
    box-shadow: 0 4px 15px rgba(255, 140, 0, 0.4);
}

.quantum-mode #quantumPauseResumeBtn.resume {
    background: linear-gradient(135deg, #32CD32, #228B22);
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.4);
}

.quantum-mode .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    box-shadow: none;
}

.quantum-mode .trading-status {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(75, 0, 130, 0.1));
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(138, 43, 226, 0.3);
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(5px);
}

.quantum-mode .status-text {
    color: var(--text-light);
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: 500;
}

.quantum-mode .trade-amount {
    color: #8A2BE2;
    font-weight: bold;
    font-size: 12px;
    text-shadow: 0 0 5px rgba(138, 43, 226, 0.5);
}

.quantum-mode .quantum-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 15px;
    position: relative;
    z-index: 1;
}

.quantum-mode .stat-item {
    background: linear-gradient(135deg, rgba(138, 43, 226, 0.15), rgba(75, 0, 130, 0.08));
    padding: 10px;
    border-radius: 6px;
    border: 1px solid rgba(138, 43, 226, 0.25);
    text-align: center;
    backdrop-filter: blur(3px);
}

.quantum-mode .stat-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.quantum-mode .stat-value {
    font-size: 14px;
    font-weight: bold;
    color: #8A2BE2;
    text-shadow: 0 0 5px rgba(138, 43, 226, 0.5);
}

/* Phoenix System Mode */
.phoenix-mode {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05));
    border-color: rgba(245, 158, 11, 0.3);
}

.phoenix-mode .mode-header {
    color: var(--warning-color);
    text-align: center;
    margin-bottom: 15px;
    font-size: 18px;
}

.phoenix-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 20px;
}

.coming-soon {
    font-size: 16px;
    color: var(--text-medium);
    text-align: center;
    font-style: italic;
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(245, 158, 11, 0.1);
    border-radius: 8px;
    border: 1px dashed rgba(245, 158, 11, 0.3);
}

/* Neural Pulse Mode */
.neural-mode {
    background: linear-gradient(135deg, rgba(11, 12, 30, 0.95), rgba(21, 26, 53, 0.95));
    border: 1px solid rgba(76, 201, 240, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin: 0;
    overflow: visible;
}

.neural-mode .mode-header {
    color: #4cc9f0;
}

/* Neural Pulse Container */
.neural-pulse-container {
    color: #ffffff;
    overflow: visible; /* Ensure no scrollbar on this container */
}

/* Neural Pulse specific styles */
.neural-mode .setting-group {
    margin-bottom: 15px;
    background: linear-gradient(135deg, rgba(21, 26, 53, 0.8), rgba(11, 12, 30, 0.8));
    padding: 14px;
    border-radius: 10px;
    border: 1px solid rgba(76, 201, 240, 0.3);
    box-shadow: 0 0 15px rgba(67, 97, 238, 0.1);
    position: relative;
    overflow: hidden;
}

.neural-mode .setting-label {
    font-weight: bold;
    color: #4cc9f0;
    margin-bottom: 8px;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
}

.neural-mode .ai-status {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

.neural-mode .ai-status i {
    margin-right: 8px;
    color: #4cc9f0;
    font-size: 16px;
}

.neural-mode .ai-confidence-container {
    position: relative;
    margin-bottom: 12px;
}

.neural-mode .ai-confidence-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 11px;
    color: var(--text-medium);
}

.neural-mode .ai-confidence-value {
    color: #4cc9f0;
    font-weight: bold;
}

.neural-mode .ai-confidence {
    height: 8px;
    background-color: rgba(11, 12, 30, 0.7);
    border-radius: 4px;
    margin-bottom: 10px;
    overflow: hidden;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
    position: relative;
}

.neural-mode .ai-confidence-bar {
    height: 100%;
    background: linear-gradient(to right, #4361ee, #4cc9f0);
    width: 75%;
    border-radius: 4px;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(76, 201, 240, 0.6);
}

.neural-mode .ai-message {
    font-style: italic;
    color: #d1d5db;
    padding: 10px;
    border-left: 3px solid #4cc9f0;
    background-color: rgba(11, 12, 30, 0.5);
    border-radius: 0 5px 5px 0;
    margin-top: 5px;
}

.neural-mode .trading-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 18px;
}

.neural-mode .action-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
}

.neural-mode #startStopBtn {
    background: linear-gradient(135deg, #10b981, #06a77d);
}

.neural-mode #pauseResumeBtn {
    background: linear-gradient(135deg, #f59e0b, #e9c46a);
    color: #333;
}

.neural-mode .trading-status {
    background: linear-gradient(135deg, rgba(11, 12, 30, 0.8), rgba(21, 26, 53, 0.8));
    padding: 14px;
    border-radius: 10px;
    border: 1px solid rgba(76, 201, 240, 0.3);
    margin-top: 10px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(67, 97, 238, 0.1);
}

.neural-mode .status-text {
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.neural-mode .trade-amount {
    color: #4cc9f0;
    font-weight: bold;
    font-size: 13px;
    display: flex;
    align-items: center;
}

.neural-mode .trade-amount::before {
    content: '→';
    margin-right: 6px;
    color: #4361ee;
    font-weight: normal;
}

.neural-mode .setting-input {
    width: 100%;
    padding: 8px 12px;
    background-color: rgba(11, 12, 30, 0.7);
    border: 1px solid rgba(76, 201, 240, 0.3);
    color: #4cc9f0;
    border-radius: 6px;
    font-weight: bold;
    text-align: right;
    transition: all 0.2s ease;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

.neural-mode select.setting-input {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234cc9f0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 30px;
}

.balance-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--medium-bg);
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

.balance {
    font-weight: bold;
    color: var(--primary-color);
}

.balance-change {
    padding: 3px 8px;
    border-radius: 10px;
    background-color: var(--success-color);
    font-size: 12px;
}

.balance-change.negative {
    background-color: var(--danger-color);
}

.settings {
    margin-bottom: 15px;
}

.setting-group {
    margin-bottom: 10px;
    background-color: var(--medium-bg);
    padding: 10px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: var(--text-medium);
}

.slider-container {
    display: flex;
    align-items: center;
}

.slider-container input {
    flex: 1;
    margin-right: 10px;
}

.setting-group select {
    width: 100%;
    padding: 5px;
    background-color: var(--light-bg);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    border-radius: 4px;
}

.trade-info {
    background-color: var(--medium-bg);
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
}

.trade-info h3 {
    font-size: 14px;
    margin-bottom: 5px;
    color: var(--primary-color);
}

#tradeInfo {
    font-size: 12px;
    color: var(--text-medium);
}

.stats-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.stat {
    flex: 1;
    text-align: center;
    background-color: var(--medium-bg);
    padding: 8px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

.stat-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 3px;
}

.stat-value {
    font-weight: bold;
    font-size: 14px;
}

.buttons {
    display: flex;
    gap: 5px;
}

.action-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    color: white;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn i {
    margin-right: 5px;
}

.buy-btn {
    background-color: var(--success-color);
}

.buy-btn:hover {
    background-color: #0ca876;
    transform: translateY(-2px);
}

.sell-btn {
    background-color: var(--danger-color);
}

.sell-btn:hover {
    background-color: #dc3545;
    transform: translateY(-2px);
}

.auto-btn {
    background-color: var(--primary-color);
}

.auto-btn:hover {
    background-color: #3b78e7;
    transform: translateY(-2px);
}

.auto-btn.active {
    background-color: var(--warning-color);
}

/* Pulse animation for balance highlight */
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.pulse-animation {
    animation: pulse 1s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .floating-container {
        width: 350px; /* Increased minimum width for smaller screens */
    }
}
