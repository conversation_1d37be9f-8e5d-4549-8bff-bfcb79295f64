/**
 * Pocket Option Bot - Page Script
 *
 * This script is injected into the page context to interact directly with the Pocket Option website.
 * It communicates with the content script via window.postMessage.
 */

// Create a global object to communicate with the content script
window.pocketOptionBot = {
    data: {},

    // Function to extract balance
    getBalance: function() {
        try {
            // Try different approaches to get the balance

            // 1. Try to get it from global variables
            if (window.balance !== undefined) {
                this.data.balance = window.balance;
                return window.balance;
            }

            // 2. Try to get it from localStorage
            const localStorageData = localStorage.getItem('account');
            if (localStorageData) {
                try {
                    const accountData = JSON.parse(localStorageData);
                    if (accountData && accountData.balance !== undefined) {
                        this.data.balance = accountData.balance;
                        return accountData.balance;
                    }
                } catch (e) {
                    console.error('Error parsing localStorage data:', e);
                }
            }

            // 3. Try to find it in the DOM with data attributes
            // First look for the specific Pocket Option balance element
            const pocketOptionBalanceElements = document.querySelectorAll('.js-balance-demo, [data-hd-show], .js-hd');
            for (const el of pocketOptionBalanceElements) {
                // Check for the data-hd-show attribute which contains the balance
                if (el.hasAttribute('data-hd-show')) {
                    const balanceValue = el.getAttribute('data-hd-show');
                    if (balanceValue && balanceValue !== '*******') {
                        // Parse the balance value, handling commas
                        const parsedBalance = parseFloat(balanceValue.replace(/,/g, ''));
                        if (!isNaN(parsedBalance)) {
                            console.log('Found Pocket Option balance from data-hd-show:', parsedBalance);
                            this.data.balance = parsedBalance;

                            // Send message to content script
                            window.postMessage({
                                from: 'pocketOptionBot',
                                action: 'balanceFound',
                                balance: parsedBalance,
                                source: 'data-hd-show'
                            }, '*');

                            return parsedBalance;
                        }
                    }
                }

                // If no data-hd-show attribute or it's masked, try the text content
                const text = el.textContent.trim();
                if (text && text !== '*******' && /[\d,.]+/.test(text)) {
                    // Parse the balance value, handling commas
                    const parsedBalance = parseFloat(text.replace(/,/g, ''));
                    if (!isNaN(parsedBalance)) {
                        console.log('Found Pocket Option balance from text content:', parsedBalance);
                        this.data.balance = parsedBalance;

                        // Send message to content script
                        window.postMessage({
                            from: 'pocketOptionBot',
                            action: 'balanceFound',
                            balance: parsedBalance,
                            source: 'text-content'
                        }, '*');

                        return parsedBalance;
                    }
                }
            }

            // Then try other generic data attributes
            const balanceElements = document.querySelectorAll('[data-balance], [data-account-balance], [data-value]');
            for (const el of balanceElements) {
                const balanceValue = el.getAttribute('data-balance') ||
                                   el.getAttribute('data-account-balance') ||
                                   el.getAttribute('data-value');
                if (balanceValue && !isNaN(parseFloat(balanceValue))) {
                    const parsedBalance = parseFloat(balanceValue);
                    this.data.balance = parsedBalance;

                    // Send message to content script
                    window.postMessage({
                        from: 'pocketOptionBot',
                        action: 'balanceFound',
                        balance: parsedBalance,
                        source: 'data-attribute'
                    }, '*');

                    return parsedBalance;
                }
            }

            // 4. Look for API calls in the network
            if (window.fetch) {
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const result = originalFetch.apply(this, args);

                    // Check if this is a balance-related API call
                    if (args[0] && typeof args[0] === 'string' &&
                        (args[0].includes('balance') || args[0].includes('account'))) {
                        result.then(response => {
                            response.clone().json().then(data => {
                                if (data && data.balance !== undefined) {
                                    window.pocketOptionBot.data.balance = data.balance;

                                    // Send message to content script
                                    window.postMessage({
                                        from: 'pocketOptionBot',
                                        action: 'balanceUpdated',
                                        balance: data.balance,
                                        source: 'api'
                                    }, '*');
                                }
                            }).catch(() => {});
                        }).catch(() => {});
                    }

                    return result;
                };
            }

            return null;
        } catch (e) {
            console.error('Error getting balance:', e);
            return null;
        }
    },

    // Function to find trading buttons
    findTradingButtons: function() {
        try {
            const buttons = {
                buy: null,
                sell: null
            };

            // Look for buttons with specific classes or text
            const allButtons = document.querySelectorAll('button');
            for (const button of allButtons) {
                const text = button.textContent.toLowerCase();
                const classes = button.className.toLowerCase();

                // Check for buy/call/up buttons
                if (text.includes('buy') || text.includes('call') || text.includes('up') ||
                    classes.includes('buy') || classes.includes('call') || classes.includes('up') ||
                    button.style.backgroundColor.includes('green')) {
                    buttons.buy = {
                        element: button,
                        text: button.textContent,
                        classes: button.className
                    };
                }

                // Check for sell/put/down buttons
                if (text.includes('sell') || text.includes('put') || text.includes('down') ||
                    classes.includes('sell') || classes.includes('put') || classes.includes('down') ||
                    button.style.backgroundColor.includes('red')) {
                    buttons.sell = {
                        element: button,
                        text: button.textContent,
                        classes: button.className
                    };
                }
            }

            this.data.tradingButtons = buttons;

            // Send message to content script
            window.postMessage({
                from: 'pocketOptionBot',
                action: 'buttonsFound',
                buttons: {
                    buy: buttons.buy ? { text: buttons.buy.text, classes: buttons.buy.classes } : null,
                    sell: buttons.sell ? { text: buttons.sell.text, classes: buttons.sell.classes } : null
                }
            }, '*');

            return buttons;
        } catch (e) {
            console.error('Error finding trading buttons:', e);
            return null;
        }
    },

    // Function to find the amount input field using multiple strategies
    findAmountInput: function() {
        console.log('Finding amount input with enhanced methods...');

        // Strategy 0: Check for the specific dropdown modal UI
        const dropdownModal = document.querySelector('.drop-down-modal.trading-panel-modal.amount-list-modal');
        if (dropdownModal) {
            console.log('Found amount dropdown modal UI');

            // Check if it's visible
            const style = window.getComputedStyle(dropdownModal);
            if (style.display !== 'none' && style.visibility !== 'hidden') {
                console.log('Amount dropdown modal is visible');

                // First check for the amount field
                const amountField = dropdownModal.querySelector('.amount-field');
                if (amountField) {
                    console.log('Found amount field in dropdown modal:', amountField.textContent);
                    // Return this as a special case that our setAmountValue function will handle
                    return {
                        isAmountModal: true,
                        element: dropdownModal,
                        amountField: amountField,
                        multiplyField: dropdownModal.querySelector('.multiply-field'),
                        virtualKeyboard: dropdownModal.querySelector('.virtual-keyboard'),
                        presetOptions: Array.from(dropdownModal.querySelectorAll('.end-block .item')).map(item => ({
                            element: item,
                            value: item.textContent.trim()
                        }))
                    };
                }
            }
        }

        // Strategy 1: Try specific Pocket Option selectors
        const pocketOptionSelectors = [
            '.block--bet-amount .value__val input',
            '.block--bet-amount input',
            '.control__value input',
            '.value__val input',
            '.bet-amount-input',
            '.amount-input',
            '.amount-field', // Added for the new UI
            '.multiply-field' // Added for the new UI
        ];

        for (const selector of pocketOptionSelectors) {
            const input = document.querySelector(selector);
            if (input && (input.tagName === 'INPUT' || input.contentEditable === 'true')) {
                console.log('Found amount input using specific selector:', selector);
                return input;
            }
        }

        // Strategy 2: Look for inputs near "Amount" text
        const amountLabels = document.querySelectorAll('div, span, label');
        for (const label of amountLabels) {
            if (label.textContent.toLowerCase().includes('amount')) {
                // Look for inputs that are siblings or children of this element
                const parent = label.parentElement;
                const inputs = parent.querySelectorAll('input');
                if (inputs.length > 0) {
                    console.log('Found amount input near "Amount" text');
                    return inputs[0];
                }

                // Look for inputs in the next sibling
                const nextSibling = label.nextElementSibling;
                if (nextSibling) {
                    const inputs = nextSibling.querySelectorAll('input');
                    if (inputs.length > 0) {
                        console.log('Found amount input in next sibling of "Amount" text');
                        return inputs[0];
                    }
                }
            }
        }

        // Strategy 3: Look for any input that might be the amount
        const allInputs = document.querySelectorAll('input[type="number"], input[type="text"]:not([type="hidden"])');
        for (const input of allInputs) {
            if (input.type === 'number' ||
                input.placeholder?.toLowerCase().includes('amount') ||
                input.className.toLowerCase().includes('amount') ||
                input.name?.toLowerCase().includes('amount') ||
                input.id?.toLowerCase().includes('amount')) {
                console.log('Found amount input using generic attributes');
                return input;
            }
        }

        // Strategy 4: Look for contentEditable elements that might be amount inputs
        const editableElements = document.querySelectorAll('[contenteditable="true"]');
        for (const el of editableElements) {
            if (el.textContent.match(/^\d+(\.\d+)?$/) ||
                el.className.toLowerCase().includes('amount') ||
                el.id?.toLowerCase().includes('amount')) {
                console.log('Found contentEditable amount element');
                return el;
            }
        }

        // Strategy 5: Look for elements that might trigger the amount modal
        const potentialTriggers = document.querySelectorAll('.amount-field, [data-amount], .bet-amount, .trade-amount');
        for (const trigger of potentialTriggers) {
            console.log('Found potential amount modal trigger:', trigger);
            return {
                isAmountTrigger: true,
                element: trigger
            };
        }

        console.log('Could not find amount input with any strategy');
        return null;
    },

    // Function to completely recreate the amount field
    recreateAmountField: function(amountField, newValue) {
        if (!amountField) return false;

        console.log('Completely recreating amount field with value:', newValue);

        try {
            // Get the parent element
            const parent = amountField.parentElement;
            if (!parent) {
                console.error('Cannot find parent element of amount field');
                return false;
            }

            // First, try to click the amount field to open any modal or dropdown
            try {
                amountField.click();
                console.log('Clicked amount field to open modal/dropdown');

                // Wait a bit for any modal to open
                setTimeout(() => {
                    // Look for any clear/reset button in the modal
                    const clearButtons = document.querySelectorAll('button, .btn, [role="button"]');
                    for (const btn of clearButtons) {
                        const text = btn.textContent.toLowerCase();
                        if (text.includes('clear') || text.includes('reset') || text.includes('delete')) {
                            btn.click();
                            console.log('Found and clicked clear/reset button');
                            break;
                        }
                    }

                    // Look for a backspace button in any virtual keyboard
                    const backspaceButtons = document.querySelectorAll('.keyboard-key, .key, .backspace, .delete');
                    for (const btn of backspaceButtons) {
                        const text = btn.textContent.toLowerCase();
                        if (text.includes('⌫') || text.includes('backspace') || text.includes('delete') ||
                            text.includes('clear') || btn.className.includes('backspace') || btn.className.includes('delete')) {
                            // Click backspace multiple times to ensure field is cleared
                            for (let i = 0; i < 20; i++) {
                                btn.click();
                            }
                            console.log('Found and clicked backspace button multiple times');
                            break;
                        }
                    }
                }, 100);
            } catch (e) {
                console.log('Error clicking amount field:', e);
            }

            // Save original attributes
            const className = amountField.className;
            const id = amountField.id;
            const attributes = {};
            for (let i = 0; i < amountField.attributes.length; i++) {
                const attr = amountField.attributes[i];
                attributes[attr.name] = attr.value;
            }

            // Create a completely new element
            const newField = document.createElement('div');

            // Copy all attributes
            newField.className = className;
            if (id) newField.id = id;
            for (const [name, value] of Object.entries(attributes)) {
                if (name !== 'class' && name !== 'id') {
                    newField.setAttribute(name, value);
                }
            }

            // Set the new value - ensure it's just the number without any previous content
            newField.textContent = newValue ? '$' + newValue : '$';

            // Replace the old element with the new one
            parent.replaceChild(newField, amountField);

            console.log('Successfully recreated amount field with new value:', newValue);

            // Dispatch events on the new field
            newField.dispatchEvent(new Event('input', { bubbles: true }));
            newField.dispatchEvent(new Event('change', { bubbles: true }));

            // Also try to directly modify the DOM using a script
            const script = document.createElement('script');
            script.textContent = `
                (function() {
                    try {
                        // Find all amount fields
                        const amountFields = document.querySelectorAll('.amount-field, [class*="amount"], [class*="bet"]');
                        console.log('Found ' + amountFields.length + ' potential amount fields');

                        for (const field of amountFields) {
                            // Completely replace the content
                            field.textContent = '$${newValue || ''}';
                            field.innerHTML = '$${newValue || ''}';

                            // Dispatch events
                            field.dispatchEvent(new Event('input', { bubbles: true }));
                            field.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    } catch (e) {
                        console.error('Error in amount field script:', e);
                    }
                })();
            `;
            document.head.appendChild(script);
            document.head.removeChild(script);

            return true;
        } catch (e) {
            console.error('Error recreating amount field:', e);
            return false;
        }
    },

    // Scorched earth approach - the most aggressive method to reset the amount field
    scorchedEarthAmountReset: function(amountInput, newValue) {
        console.log('SCORCHED EARTH: Executing most aggressive amount reset for value:', newValue);

        try {
            // Step 1: Try to identify all possible amount-related elements
            let amountField = null;

            if (amountInput.isAmountModal && amountInput.amountField) {
                amountField = amountInput.amountField;
            } else if (!amountInput.isAmountTrigger) {
                amountField = amountInput;
            }

            // If we couldn't get a direct reference, search for it
            if (!amountField) {
                const possibleFields = document.querySelectorAll('.amount-field, [class*="amount"], [class*="bet"]');
                if (possibleFields.length > 0) {
                    amountField = possibleFields[0];
                }
            }

            if (amountField) {
                // Step 2: Try multiple aggressive clearing techniques in sequence

                // 2.1: First try to completely remove the element from DOM
                const parent = amountField.parentElement;
                if (parent) {
                    // Save attributes for recreation
                    const className = amountField.className;
                    const id = amountField.id;
                    const attributes = {};
                    for (let i = 0; i < amountField.attributes.length; i++) {
                        const attr = amountField.attributes[i];
                        attributes[attr.name] = attr.value;
                    }

                    // Remove the element completely
                    parent.removeChild(amountField);

                    // Force browser reflow
                    parent.offsetHeight;

                    // Wait a moment for any event handlers to process the removal
                    setTimeout(() => {
                        // Create a completely new element
                        const newField = document.createElement('div');

                        // Copy attributes
                        newField.className = className;
                        if (id) newField.id = id;
                        for (const [name, value] of Object.entries(attributes)) {
                            if (name !== 'class' && name !== 'id') {
                                newField.setAttribute(name, value);
                            }
                        }

                        // Set the value directly
                        newField.textContent = newValue ? '$' + newValue : '$';

                        // Add it back to the DOM
                        parent.appendChild(newField);

                        // Dispatch events
                        newField.dispatchEvent(new Event('input', { bubbles: true }));
                        newField.dispatchEvent(new Event('change', { bubbles: true }));

                        console.log('SCORCHED EARTH: Successfully recreated amount field with value:', newValue);
                    }, 100);
                }

                // 2.2: Also try to inject a script that will find and modify ALL possible amount fields
                const script = document.createElement('script');
                script.textContent = `
                    (function() {
                        try {
                            console.log('SCORCHED EARTH SCRIPT: Starting aggressive DOM manipulation');

                            // Find ALL possible amount-related elements
                            const amountElements = document.querySelectorAll('.amount-field, [class*="amount"], [class*="bet"], [id*="amount"], [id*="bet"], [name*="amount"], [name*="bet"]');
                            console.log('Found ' + amountElements.length + ' potential amount-related elements');

                            // Process each element
                            for (const el of amountElements) {
                                try {
                                    // First try to completely clear the element
                                    if (el.tagName === 'INPUT') {
                                        // For input elements
                                        el.value = '';
                                        el.setAttribute('value', '');

                                        // Force events
                                        el.dispatchEvent(new Event('input', { bubbles: true }));
                                        el.dispatchEvent(new Event('change', { bubbles: true }));

                                        // Now set the new value
                                        setTimeout(() => {
                                            el.value = '${newValue}';
                                            el.setAttribute('value', '${newValue}');
                                            el.dispatchEvent(new Event('input', { bubbles: true }));
                                            el.dispatchEvent(new Event('change', { bubbles: true }));
                                        }, 50);
                                    } else {
                                        // For other elements
                                        // Remove all child nodes
                                        while (el.firstChild) {
                                            el.removeChild(el.firstChild);
                                        }

                                        // Set to just the currency symbol
                                        el.textContent = '$';
                                        el.innerHTML = '$';

                                        // Force events
                                        el.dispatchEvent(new Event('input', { bubbles: true }));
                                        el.dispatchEvent(new Event('change', { bubbles: true }));

                                        // Now set the new value
                                        setTimeout(() => {
                                            el.textContent = '$${newValue}';
                                            el.innerHTML = '$${newValue}';
                                            el.dispatchEvent(new Event('input', { bubbles: true }));
                                            el.dispatchEvent(new Event('change', { bubbles: true }));
                                        }, 50);
                                    }
                                } catch (err) {
                                    console.error('Error processing element:', err);
                                }
                            }

                            // Also try to find and modify any global variables related to amount
                            for (const key in window) {
                                if (key.toLowerCase().includes('amount') || key.toLowerCase().includes('bet')) {
                                    try {
                                        window[key] = ${newValue};
                                        console.log('Set global variable ' + key + ' to ${newValue}');
                                    } catch (e) {
                                        // Ignore errors for read-only properties
                                    }
                                }
                            }

                            // Also try to find any hidden form fields
                            const hiddenFields = document.querySelectorAll('input[type="hidden"]');
                            for (const field of hiddenFields) {
                                if (field.name && (field.name.includes('amount') || field.name.includes('bet'))) {
                                    field.value = '${newValue}';
                                }
                            }

                            console.log('SCORCHED EARTH SCRIPT: Completed aggressive DOM manipulation');
                        } catch (e) {
                            console.error('Error in scorched earth script:', e);
                        }
                    })();
                `;
                document.head.appendChild(script);
                document.head.removeChild(script);
            }

            return true;
        } catch (e) {
            console.error('Error in scorched earth approach:', e);
            return false;
        }
    },

    // Function to completely nuke and recreate the amount field
    nukeAmountField: function(amountField, newValue = '') {
        if (!amountField) return false;

        console.log('NUCLEAR OPTION: Completely nuking and recreating amount field');

        try {
            // Get the parent element
            const parent = amountField.parentElement;
            if (!parent) {
                console.error('Cannot find parent element of amount field');
                return false;
            }

            // Save original attributes and position
            const className = amountField.className;
            const id = amountField.id;
            const attributes = {};
            for (let i = 0; i < amountField.attributes.length; i++) {
                const attr = amountField.attributes[i];
                attributes[attr.name] = attr.value;
            }

            // Get the index of the amount field among its siblings
            const siblings = Array.from(parent.children);
            const index = siblings.indexOf(amountField);

            // Remove the original element completely
            parent.removeChild(amountField);

            // Force a reflow to ensure the DOM is updated
            parent.offsetHeight;

            // Create a completely new element with the same attributes
            const newField = document.createElement('div');

            // Copy all attributes
            newField.className = className;
            if (id) newField.id = id;
            for (const [name, value] of Object.entries(attributes)) {
                if (name !== 'class' && name !== 'id') {
                    newField.setAttribute(name, value);
                }
            }

            // Set the new value - ensure it's just the currency symbol or currency symbol + value
            newField.textContent = newValue ? '$' + newValue : '$';

            // Insert the new element at the same position
            if (index >= 0 && index < siblings.length) {
                parent.insertBefore(newField, siblings[index]);
            } else {
                parent.appendChild(newField);
            }

            console.log('Successfully nuked and recreated amount field with value:', newValue);

            // Dispatch events on the new field
            newField.dispatchEvent(new Event('input', { bubbles: true }));
            newField.dispatchEvent(new Event('change', { bubbles: true }));

            // Return the new field for further operations
            return newField;
        } catch (e) {
            console.error('Error nuking amount field:', e);
            return false;
        }
    },

    // Function to ensure the amount field is completely cleared
    ensureAmountFieldCleared: function(amountField) {
        if (!amountField) return false;

        console.log('Ensuring amount field is completely cleared');

        // Save the original value for debugging
        const originalValue = amountField.textContent;
        console.log('Original amount field value:', originalValue);

        // First try the nuclear option
        const nukedField = this.nukeAmountField(amountField);
        if (nukedField) {
            console.log('Successfully cleared amount field using nuclear option');
            // Return the new field
            return nukedField;
        }

        // If nuclear option fails, try to completely recreate the field
        if (this.recreateAmountField(amountField, '')) {
            console.log('Successfully cleared amount field by recreating it');
            return true;
        }

        // If recreation fails, try multiple clearing approaches
        const clearingApproaches = [
            // Approach 1: Set to empty or just the currency symbol with a delay
            () => {
                amountField.textContent = '$';
                amountField.dispatchEvent(new Event('input', { bubbles: true }));
                amountField.dispatchEvent(new Event('change', { bubbles: true }));
                // Force a reflow
                amountField.offsetHeight;
                return new Promise(resolve => setTimeout(resolve, 50));
            },
            // Approach 2: Use innerHTML with a delay
            () => {
                amountField.innerHTML = '$';
                amountField.dispatchEvent(new Event('input', { bubbles: true }));
                amountField.dispatchEvent(new Event('change', { bubbles: true }));
                // Force a reflow
                amountField.offsetHeight;
                return new Promise(resolve => setTimeout(resolve, 50));
            },
            // Approach 3: Use script injection with more aggressive clearing
            () => {
                const script = document.createElement('script');
                script.textContent = `
                    (function() {
                        const field = document.querySelector('.amount-field');
                        if (field) {
                            // Try multiple clearing techniques
                            field.textContent = '$';
                            field.innerHTML = '$';

                            // Force a reflow
                            field.offsetHeight;

                            // Try to remove all child nodes
                            while (field.firstChild) {
                                field.removeChild(field.firstChild);
                            }

                            // Add just the currency symbol
                            field.appendChild(document.createTextNode('$'));

                            // Dispatch events
                            field.dispatchEvent(new Event('input', { bubbles: true }));
                            field.dispatchEvent(new Event('change', { bubbles: true }));

                            // Try to find any hidden input fields that might be storing the value
                            const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
                            for (const input of hiddenInputs) {
                                if (input.name && (input.name.includes('amount') || input.name.includes('bet'))) {
                                    input.value = '';
                                    input.setAttribute('value', '');
                                }
                            }
                        }
                    })();
                `;
                document.head.appendChild(script);
                document.head.removeChild(script);
                return new Promise(resolve => setTimeout(resolve, 50));
            },
            // Approach 4: Try to replace the entire content with regex
            () => {
                const parent = amountField.parentElement;
                if (parent) {
                    const originalHTML = parent.innerHTML;
                    // More aggressive regex to remove all digits
                    const newHTML = originalHTML.replace(/>\s*\$?\s*\d+\.?\d*\s*</g, '>$<');
                    parent.innerHTML = newHTML;
                }
                return new Promise(resolve => setTimeout(resolve, 50));
            },
            // Approach 5: Try to use DOM manipulation to recreate the element with a delay
            () => {
                const parent = amountField.parentElement;
                if (parent) {
                    const newField = document.createElement('div');
                    newField.className = amountField.className;
                    newField.textContent = '$';
                    parent.replaceChild(newField, amountField);
                    // Update our reference to the new field
                    amountField = newField;
                }
                return new Promise(resolve => setTimeout(resolve, 50));
            },
            // Approach 6: Try to use the click event to open the amount modal and then close it
            () => {
                // Click the amount field to open the modal
                amountField.click();

                // Wait a bit and then click elsewhere to close the modal
                return new Promise(resolve => {
                    setTimeout(() => {
                        document.body.click();
                        resolve();
                    }, 100);
                });
            },
            // Approach 7: Try to use MutationObserver to detect and prevent changes
            () => {
                return new Promise(resolve => {
                    // Create a MutationObserver to watch for changes
                    const observer = new MutationObserver((mutations) => {
                        mutations.forEach((mutation) => {
                            if (mutation.type === 'characterData' || mutation.type === 'childList') {
                                // If content changes and contains digits, force it back to just $
                                const content = amountField.textContent;
                                if (/\d/.test(content)) {
                                    amountField.textContent = '$';
                                }
                            }
                        });
                    });

                    // Start observing
                    observer.observe(amountField, {
                        characterData: true,
                        childList: true,
                        subtree: true
                    });

                    // Set to $ and force events
                    amountField.textContent = '$';
                    amountField.dispatchEvent(new Event('input', { bubbles: true }));
                    amountField.dispatchEvent(new Event('change', { bubbles: true }));

                    // Stop observing after a short time
                    setTimeout(() => {
                        observer.disconnect();
                        resolve();
                    }, 100);
                });
            }
        ];

        // Try each approach with async/await to ensure delays work
        const tryApproaches = async () => {
            for (let i = 0; i < clearingApproaches.length; i++) {
                try {
                    await clearingApproaches[i]();

                    // Check if the field is cleared
                    const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                    console.log(`After approach ${i+1}, amount field value:`, currentValue);

                    if (!currentValue || currentValue === '') {
                        console.log(`Successfully cleared amount field using approach ${i+1}`);
                        return true;
                    }
                } catch (e) {
                    console.error(`Error with clearing approach ${i+1}:`, e);
                }
            }
            return false;
        };

        // Execute the approaches and return a promise
        tryApproaches().then(success => {
            if (!success) {
                console.warn('Failed to clear amount field with all approaches');
            }
        });

        // For backward compatibility, return the field or true
        return amountField || true;
    },

    // Function to interact with the virtual keyboard in the amount modal
    interactWithVirtualKeyboard: function(keyboard, amount) {
        console.log('Interacting with virtual keyboard to input:', amount);

        if (!keyboard) {
            console.error('No virtual keyboard provided');
            return false;
        }

        try {
            // First, try to clear the amount field directly using our nuclear approach
            const amountField = document.querySelector('.amount-field');
            if (amountField) {
                console.log('Using nuclear approach to clear amount field before keyboard interaction');
                // Try our nuclear option first
                const nukedField = this.nukeAmountField(amountField);
                if (!nukedField) {
                    // If nuclear option fails, fall back to enhanced clearing
                    this.ensureAmountFieldCleared(amountField);
                }

                // Wait a bit to ensure clearing takes effect - use setTimeout instead of await
                setTimeout(() => {
                    // Continue with keyboard interaction after delay
                    this._continueKeyboardInteraction(keyboard, amount, amountField);
                }, 100);

                return true; // Return early since we're using setTimeout
            }

            // If no amount field found, continue with normal flow
            return this._continueKeyboardInteraction(keyboard, amount);
        } catch (e) {
            console.error('Error in keyboard interaction:', e);
            return false;
        }
    },

    // Helper function to continue keyboard interaction after clearing
    _continueKeyboardInteraction: function(keyboard, amount, amountField) {
        try {
            // Clear any existing value by clicking the backspace button
            const backspaceButton = keyboard.querySelector('.virtual-keyboard__input svg[data-src*="back.svg"]');
            if (backspaceButton) {
                const backspaceParent = backspaceButton.closest('.virtual-keyboard__input');
                if (backspaceParent) {
                    console.log('Found backspace button, clicking multiple times to ensure field is cleared');
                    // Click backspace many more times to ensure the field is completely cleared
                    for (let i = 0; i < 30; i++) {
                        backspaceParent.click();
                        // Add a small delay between clicks
                        if (i % 5 === 0) {
                            // Use setTimeout to create a small delay
                            setTimeout(() => {}, 50);
                        }
                    }
                }
            }

            // If we don't have an amount field reference, try to find it
            if (!amountField) {
                amountField = document.querySelector('.amount-field');
            }

            // Check if the field is cleared
            if (amountField) {
                const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                console.log('Amount field value after backspace clearing:', currentValue);

                // If not cleared, try our nuclear option
                if (currentValue && currentValue !== '') {
                    console.log('Field not cleared by backspace, trying nuclear option');
                    const nukedField = this.nukeAmountField(amountField);
                    if (nukedField) {
                        // Update our reference
                        amountField = nukedField;
                    } else {
                        // If nuclear option fails, try enhanced clearing
                        this.ensureAmountFieldCleared(amountField);
                    }
                }
            }

            // Now input each digit of the amount, but only after ensuring the field is clear
            console.log('Starting to input digits for amount:', amount);
            const amountStr = amount.toString();

            // Verify the field is clear before proceeding
            if (amountField) {
                const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                if (currentValue && currentValue !== '') {
                    console.warn('Field still not cleared, setting directly before keyboard input');
                    amountField.textContent = '$';
                    amountField.innerHTML = '$';

                    // Force a reflow
                    amountField.offsetHeight;
                }
            }

            // Now input each digit with proper delays
            const inputDigits = async () => {
                for (let i = 0; i < amountStr.length; i++) {
                    const char = amountStr[i];
                    // Find the button for this digit
                    const digitButtons = keyboard.querySelectorAll('.virtual-keyboard__input');
                    let found = false;

                    for (const button of digitButtons) {
                        if (button.textContent.trim() === char) {
                            console.log('Clicking virtual keyboard button for:', char);
                            button.click();
                            // Add a proper delay between clicks
                            await new Promise(resolve => setTimeout(resolve, 100));
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        console.warn(`Could not find virtual keyboard button for: ${char}`);
                    }

                    // Verify after each digit that we're not getting unexpected values
                    if (amountField && i < amountStr.length - 1) {
                        const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                        const expectedPartial = amountStr.substring(0, i+1);
                        console.log(`After digit ${i+1}, field value: ${currentValue}, expected: ${expectedPartial}`);

                        // If we're getting unexpected values, try direct setting
                        if (!currentValue.endsWith(expectedPartial)) {
                            console.warn('Unexpected value during keyboard input, trying direct setting');
                            // Use nuclear option for direct setting
                            this.nukeAmountField(amountField, amountStr);
                            return; // Exit the loop since we've set the full value
                        }
                    }
                }

                // Final verification after all digits
                await new Promise(resolve => setTimeout(resolve, 200));

                if (amountField) {
                    const finalValue = amountField.textContent.replace(/[^0-9.]/g, '');
                    console.log('Final amount field value after keyboard input:', finalValue);

                    // If the value doesn't match what we want, try nuclear option
                    if (finalValue !== amountStr) {
                        console.log('Final value does not match desired amount, trying nuclear option');
                        this.nukeAmountField(amountField, amountStr);
                    } else {
                        console.log('KEYBOARD INPUT SUCCESS: Value matches expected amount');
                    }
                }
            };

            // Start the digit input process
            inputDigits().catch(e => console.error('Error in digit input:', e));

            // For backward compatibility, return true
            return true;
        } catch (e) {
            console.error('Error in keyboard interaction continuation:', e);

            // Try direct setting as a fallback
            if (amountField) {
                this.nukeAmountField(amountField, amount.toString());
            }

            return false;
        }
    },

    // Function to set the amount using multiple approaches
    setAmountValue: function(input, amount) {
        console.log('Setting amount value with enhanced methods:', amount);
        const amountStr = amount.toString();

        // FIRST: Try the nuclear approach - most reliable for complete replacement
        console.log('Attempting nuclear amount replacement first');
        const nuclearSuccess = this.nuclearAmountReplacement(input, amount);
        if (nuclearSuccess) {
            console.log('Nuclear replacement successful, verifying...');

            // Verify the amount was set correctly
            let actualAmount;
            if (input.isAmountModal && input.amountField) {
                actualAmount = input.amountField.textContent.replace(/[^0-9.]/g, '');
            } else if (input.tagName === 'INPUT') {
                actualAmount = input.value;
            } else {
                actualAmount = input.textContent.replace(/[^0-9.]/g, '');
            }

            const expectedNumber = parseFloat(amountStr);
            const actualNumber = parseFloat(actualAmount);

            if (!isNaN(expectedNumber) && !isNaN(actualNumber) && Math.abs(expectedNumber - actualNumber) < 0.001) {
                console.log('Nuclear replacement verified successfully');
                return true;
            } else {
                console.warn('Nuclear replacement verification failed, trying fallback methods');
            }
        }

        // FALLBACK: Special handling for the amount modal UI
        if (input && input.isAmountModal) {
            console.log('Handling amount modal UI');

            try {
                const modal = input.element;
                const amountField = input.amountField;
                const virtualKeyboard = input.virtualKeyboard;
                const presetOptions = input.presetOptions;

                // First try to use preset options if they match our desired amount
                if (presetOptions && presetOptions.length > 0) {
                    console.log('Checking preset options:', presetOptions.map(o => o.value));

                    // Look for an exact match
                    for (const option of presetOptions) {
                        const optionValue = option.value.replace(/[^0-9.]/g, ''); // Remove currency symbols
                        if (optionValue === amountStr || optionValue === parseFloat(amountStr).toString()) {
                            console.log('Found matching preset option:', option.value);
                            option.element.click();
                            return true;
                        }
                    }
                }

                // If no preset matches, try using the virtual keyboard
                if (virtualKeyboard) {
                    console.log('Using virtual keyboard to input amount');
                    const success = this.interactWithVirtualKeyboard(virtualKeyboard, amount);
                    if (success) {
                        console.log('Successfully set amount using virtual keyboard');
                        return true;
                    }
                }

                // If virtual keyboard fails, try directly modifying the amount field
                if (amountField) {
                    console.log('Directly modifying amount field:', amountField);

                    // Try to remove the currency symbol
                    const currentValue = amountField.textContent.replace(/[^0-9.]/g, '');
                    console.log('Current amount field value (cleaned):', currentValue);

                    // First try the nuclear option - completely nuke and recreate with the new value
                    const nukedField = this.nukeAmountField(amountField, amountStr);
                    if (nukedField) {
                        console.log('Successfully set amount using nuclear option');

                        // Also try to find and modify the multiply field
                        const multiplyField = document.querySelector('.multiply-field');
                        if (multiplyField) {
                            multiplyField.value = '1'; // Reset multiplier to 1
                            multiplyField.dispatchEvent(new Event('input', { bubbles: true }));
                            multiplyField.dispatchEvent(new Event('change', { bubbles: true }));
                        }

                        // Verify the value was set correctly
                        setTimeout(() => {
                            const verifyField = document.querySelector('.amount-field');
                            if (verifyField) {
                                const verifyValue = verifyField.textContent.replace(/[^0-9.]/g, '');
                                console.log('Verification after nuclear option:', verifyValue);

                                if (verifyValue !== amountStr) {
                                    console.warn('Nuclear option verification failed, trying fallback');
                                    // Try direct setting as a fallback
                                    verifyField.textContent = '$' + amountStr;
                                    verifyField.innerHTML = '$' + amountStr;
                                    verifyField.dispatchEvent(new Event('input', { bubbles: true }));
                                    verifyField.dispatchEvent(new Event('change', { bubbles: true }));
                                }
                            }
                        }, 100);

                        return true;
                    }

                    // If nuclear option fails, try to completely recreate the field
                    if (this.recreateAmountField(amountField, amountStr)) {
                        console.log('Successfully set amount by recreating the field');

                        // Also try to find and modify the multiply field
                        const multiplyField = document.querySelector('.multiply-field');
                        if (multiplyField) {
                            multiplyField.value = '1'; // Reset multiplier to 1
                            multiplyField.dispatchEvent(new Event('input', { bubbles: true }));
                            multiplyField.dispatchEvent(new Event('change', { bubbles: true }));
                        }

                        return true;
                    }

                    // If recreation fails, try to ensure the field is completely cleared
                    // This returns the cleared field or true
                    const clearedField = this.ensureAmountFieldCleared(amountField);

                    // Wait a bit to ensure clearing takes effect
                    setTimeout(() => {
                        // Get the current field (might be a new one from clearing)
                        const currentField = (clearedField !== true) ? clearedField : document.querySelector('.amount-field');
                        if (currentField) {
                            // Set the new value after ensuring it's clear
                            currentField.textContent = '$' + amountStr;
                            currentField.innerHTML = '$' + amountStr;
                            currentField.dispatchEvent(new Event('input', { bubbles: true }));
                            currentField.dispatchEvent(new Event('change', { bubbles: true }));
                        }
                    }, 100);

                    // Create a script to modify the amount field with more aggressive clearing
                    const script = document.createElement('script');
                    script.textContent = `
                        (function() {
                            try {
                                // Find the amount field
                                let amountField = document.querySelector('.amount-field');
                                if (amountField) {
                                    console.log('ULTRA AGGRESSIVE CLEARING: Starting nuclear approach via script injection');

                                    // Get the parent element
                                    const parent = amountField.parentElement;
                                    if (parent) {
                                        // Save original attributes
                                        const className = amountField.className;
                                        const id = amountField.id;
                                        const attributes = {};
                                        for (let i = 0; i < amountField.attributes.length; i++) {
                                            const attr = amountField.attributes[i];
                                            attributes[attr.name] = attr.value;
                                        }

                                        // Get the index of the amount field among its siblings
                                        const siblings = Array.from(parent.children);
                                        const index = siblings.indexOf(amountField);

                                        // COMPLETELY REMOVE THE ORIGINAL ELEMENT
                                        parent.removeChild(amountField);

                                        // Force a reflow to ensure the DOM is updated
                                        parent.offsetHeight;

                                        // Create a completely new element with the same attributes
                                        const newField = document.createElement('div');

                                        // Copy all attributes
                                        newField.className = className;
                                        if (id) newField.id = id;
                                        for (const name in attributes) {
                                            if (name !== 'class' && name !== 'id') {
                                                newField.setAttribute(name, attributes[name]);
                                            }
                                        }

                                        // Insert the new element at the same position
                                        if (index >= 0 && index < siblings.length) {
                                            parent.insertBefore(newField, siblings[index]);
                                        } else {
                                            parent.appendChild(newField);
                                        }

                                        // Update our reference
                                        amountField = newField;

                                        console.log('NUCLEAR APPROACH: Successfully nuked and recreated amount field');
                                    }

                                    // Wait to ensure the DOM is updated
                                    setTimeout(() => {
                                        // Make sure we have the latest reference
                                        const currentField = document.querySelector('.amount-field');
                                        if (currentField) {
                                            // Set the value with a delay to ensure the field is ready
                                            currentField.textContent = '$${amountStr}';
                                            console.log('Set amount field text to: $${amountStr}');

                                            // Dispatch events
                                            currentField.dispatchEvent(new Event('input', { bubbles: true }));
                                            currentField.dispatchEvent(new Event('change', { bubbles: true }));

                                            // Also try innerHTML
                                            currentField.innerHTML = '$${amountStr}';

                                            // Verify after a short delay
                                            setTimeout(() => {
                                                // Get the latest field again
                                                const verifyField = document.querySelector('.amount-field');
                                                if (verifyField) {
                                                    const verifyValue = verifyField.textContent.replace(/[^0-9.]/g, '');
                                                    console.log('Verification value:', verifyValue);

                                                    if (verifyValue !== '${amountStr}') {
                                                        console.warn('Value verification failed, trying one last direct approach');

                                                        // Try direct setting one more time
                                                        verifyField.textContent = '$${amountStr}';
                                                        verifyField.innerHTML = '$${amountStr}';
                                                        verifyField.dispatchEvent(new Event('input', { bubbles: true }));
                                                        verifyField.dispatchEvent(new Event('change', { bubbles: true }));

                                                        // Also try to find any hidden inputs that might be storing the value
                                                        const hiddenInputs = document.querySelectorAll('input[type="hidden"]');
                                                        for (const input of hiddenInputs) {
                                                            if (input.name && (input.name.includes('amount') || input.name.includes('bet'))) {
                                                                input.value = '${amountStr}';
                                                                input.setAttribute('value', '${amountStr}');
                                                            }
                                                        }
                                                    }
                                                }
                                            }, 100);
                                        }
                                    }, 100);
                                }

                                // Also try to find and modify the multiply field
                                const multiplyField = document.querySelector('.multiply-field');
                                if (multiplyField) {
                                    multiplyField.value = '1'; // Reset multiplier to 1
                                    multiplyField.dispatchEvent(new Event('input', { bubbles: true }));
                                    multiplyField.dispatchEvent(new Event('change', { bubbles: true }));
                                }

                                // Also try to find any amount-related inputs
                                const amountInputs = document.querySelectorAll('input[class*="amount"], input[class*="bet"], input[name*="amount"], input[name*="bet"]');
                                for (const input of amountInputs) {
                                    input.value = '${amountStr}';
                                    input.setAttribute('value', '${amountStr}');
                                    input.dispatchEvent(new Event('input', { bubbles: true }));
                                    input.dispatchEvent(new Event('change', { bubbles: true }));
                                }
                            } catch(e) {
                                console.error('Error in nuclear approach:', e);
                            }
                        })();
                    `;
                    document.head.appendChild(script);
                    document.head.removeChild(script);

                    // Double-check after a longer delay to ensure all previous operations have completed
                    setTimeout(() => {
                        try {
                            console.log('FINAL VERIFICATION: Checking amount field value after all operations');

                            // Find the amount field again as it might have been recreated
                            const currentAmountField = document.querySelector('.amount-field');
                            if (currentAmountField) {
                                const finalValue = currentAmountField.textContent.replace(/[^0-9.]/g, '');
                                console.log('Final amount field value after all modifications:', finalValue);

                                if (finalValue !== amountStr) {
                                    console.log('CRITICAL: Final value still doesn\\'t match, executing last resort approach');

                                    // LAST RESORT: Try the nuclear option one more time with a direct value set
                                    const lastResortField = this.nukeAmountField(currentAmountField, amountStr);

                                    if (lastResortField) {
                                        console.log('Successfully applied last resort nuclear option');

                                        // Also try to directly modify any other amount-related elements
                                        const script = document.createElement('script');
                                        script.textContent = `
                                            (function() {
                                                try {
                                                    // Try to find ALL possible amount-related elements
                                                    const amountElements = document.querySelectorAll('[class*="amount"], [class*="bet"], [id*="amount"], [id*="bet"], [name*="amount"], [name*="bet"]');
                                                    console.log('Found ' + amountElements.length + ' potential amount-related elements');

                                                    for (const el of amountElements) {
                                                        if (el.tagName === 'INPUT') {
                                                            el.value = '${amountStr}';
                                                            el.setAttribute('value', '${amountStr}');
                                                        } else {
                                                            // For non-input elements, set textContent and innerHTML
                                                            if (el.className.includes('amount') || el.className.includes('bet')) {
                                                                el.textContent = '$${amountStr}';
                                                                el.innerHTML = '$${amountStr}';
                                                            }
                                                        }

                                                        // Dispatch events
                                                        el.dispatchEvent(new Event('input', { bubbles: true }));
                                                        el.dispatchEvent(new Event('change', { bubbles: true }));
                                                    }

                                                    // Also check for any global JavaScript variables related to amount
                                                    for (const key in window) {
                                                        if (key.toLowerCase().includes('amount') || key.toLowerCase().includes('bet')) {
                                                            try {
                                                                window[key] = ${amountStr};
                                                                console.log('Set global variable ' + key + ' to ${amountStr}');
                                                            } catch (e) {
                                                                // Ignore errors for read-only properties
                                                            }
                                                        }
                                                    }
                                                } catch (e) {
                                                    console.error('Error in last resort script:', e);
                                                }
                                            })();
                                        `;
                                        document.head.appendChild(script);
                                        document.head.removeChild(script);
                                    }
                                } else {
                                    console.log('VERIFICATION SUCCESS: Amount field contains the correct value');
                                }
                            } else {
                                console.error('VERIFICATION FAILED: Could not find amount field for final verification');
                            }
                        } catch (e) {
                            console.error('Error in final verification:', e);
                        }
                    }, 500);

                    return true;
                }

                // If we get here, we couldn't set the amount in the modal
                console.error('Failed to set amount in modal UI');
                return false;
            } catch (e) {
                console.error('Error handling amount modal UI:', e);
                return false;
            }
        }

        // Special handling for amount triggers
        if (input && input.isAmountTrigger) {
            console.log('Handling amount trigger element');

            try {
                // Click the trigger to open the modal
                input.element.click();
                console.log('Clicked amount trigger element');

                // Wait a short time for the modal to appear
                setTimeout(() => {
                    // Try to find the modal now that it should be open
                    const modal = document.querySelector('.drop-down-modal.trading-panel-modal.amount-list-modal');
                    if (modal) {
                        console.log('Modal appeared after clicking trigger');

                        // Create a new input object with the modal details
                        const modalInput = {
                            isAmountModal: true,
                            element: modal,
                            amountField: modal.querySelector('.amount-field'),
                            multiplyField: modal.querySelector('.multiply-field'),
                            virtualKeyboard: modal.querySelector('.virtual-keyboard'),
                            presetOptions: Array.from(modal.querySelectorAll('.end-block .item')).map(item => ({
                                element: item,
                                value: item.textContent.trim()
                            }))
                        };

                        // Now set the amount using the modal
                        this.setAmountValue(modalInput, amount);
                    }
                }, 500);

                return true;
            } catch (e) {
                console.error('Error handling amount trigger:', e);
                return false;
            }
        }

        // Regular input handling for standard input elements
        // Store original values for debugging
        const originalValue = input.value || input.textContent;
        console.log('Original value:', originalValue);

        // Strategy 1: Direct value setting
        try {
            if (input.tagName === 'INPUT') {
                // For regular input elements
                input.value = amountStr;
                input.setAttribute('value', amountStr);
            } else {
                // For contentEditable elements
                input.textContent = amountStr;
            }
            console.log('Direct value set to:', amountStr);
        } catch (e) {
            console.error('Error in direct value setting:', e);
        }

        // Strategy 2: Dispatch events to ensure the change is recognized
        try {
            // Focus the element first
            input.focus();

            // Clear existing value
            if (input.tagName === 'INPUT') {
                input.value = '';
                input.dispatchEvent(new Event('input', { bubbles: true }));
            } else {
                input.textContent = '';
            }

            // Set the new value
            if (input.tagName === 'INPUT') {
                input.value = amountStr;
            } else {
                input.textContent = amountStr;
            }

            // Dispatch events
            const inputEvent = new Event('input', { bubbles: true });
            const changeEvent = new Event('change', { bubbles: true });
            input.dispatchEvent(inputEvent);
            input.dispatchEvent(changeEvent);

            // Also try with KeyboardEvent
            for (const char of amountStr) {
                const keyEvent = new KeyboardEvent('keydown', {
                    key: char,
                    code: 'Key' + char.toUpperCase(),
                    bubbles: true
                });
                input.dispatchEvent(keyEvent);
            }

            console.log('Events dispatched for value:', amountStr);
        } catch (e) {
            console.error('Error dispatching events:', e);
        }

        // Strategy 3: Use execCommand for contentEditable elements
        try {
            if (document.execCommand) {
                input.focus();
                document.execCommand('selectAll', false, null);
                document.execCommand('delete', false, null);
                document.execCommand('insertText', false, amountStr);
                console.log('Used execCommand to set value');
            }
        } catch (e) {
            console.error('Error using execCommand:', e);
        }

        // Strategy 4: Use JavaScript property setters and getters
        try {
            // Get the property descriptor
            const inputProto = Object.getPrototypeOf(input);
            const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'value');

            if (descriptor && descriptor.set) {
                // Call the setter directly
                descriptor.set.call(input, amountStr);
                console.log('Used property setter to set value');
            }
        } catch (e) {
            console.error('Error using property setter:', e);
        }

        // Strategy 5: Modify the element using a script tag
        try {
            const id = 'input-' + Math.random().toString(36).substring(2, 15);
            input.id = id;

            const script = document.createElement('script');
            script.textContent = `
                (function() {
                    const input = document.getElementById('${id}');
                    if (input) {
                        if (input.tagName === 'INPUT') {
                            input.value = '${amountStr}';
                        } else {
                            input.textContent = '${amountStr}';
                        }

                        // Trigger events
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));

                        // Try to trigger any custom handlers
                        if (typeof jQuery !== 'undefined') {
                            jQuery(input).trigger('input').trigger('change');
                        }
                    }
                })();
            `;
            document.head.appendChild(script);
            document.head.removeChild(script);
            console.log('Used script injection to set value');
        } catch (e) {
            console.error('Error using script injection:', e);
        }

        // Log the final value
        const finalValue = input.value || input.textContent;
        console.log('Final value after all strategies:', finalValue);

        return finalValue === amountStr || finalValue === parseFloat(amountStr).toString();
    },

    // Function to execute a trade with safety checks
    executeTrade: function(direction, amount) {
        try {
            console.log('Executing trade with enhanced methods:', direction, amount);

            // Find the amount input
            const amountInput = this.findAmountInput();

            if (amountInput) {
                // Set the amount using our enhanced method
                const success = this.setAmountValue(amountInput, amount);
                console.log('Amount set success:', success);

                // CRITICAL SAFETY CHECK: Verify the amount is exactly what we expect before trading
                let actualAmount;
                let amountVerified = false;

                // Get the actual amount that will be traded
                if (amountInput.isAmountModal && amountInput.amountField) {
                    actualAmount = amountInput.amountField.textContent.replace(/[^0-9.]/g, '');
                } else if (amountInput.tagName === 'INPUT') {
                    actualAmount = amountInput.value;
                } else {
                    actualAmount = amountInput.textContent.replace(/[^0-9.]/g, '');
                }

                console.log('SAFETY CHECK - Expected amount:', amount, 'Actual amount:', actualAmount);

                // Convert both to numbers for comparison (handle strings like "1" vs "1.0")
                const expectedNumber = parseFloat(amount);
                const actualNumber = parseFloat(actualAmount);

                // Check if the amounts match exactly
                if (!isNaN(expectedNumber) && !isNaN(actualNumber)) {
                    if (Math.abs(expectedNumber - actualNumber) < 0.001) { // Allow tiny floating point differences
                        console.log('SAFETY CHECK PASSED: Amount verified');
                        amountVerified = true;
                    } else {
                        console.error('SAFETY CHECK FAILED: Amount mismatch! Expected:', expectedNumber, 'Actual:', actualNumber);

                        // Try one more time to set the correct amount
                        console.log('EMERGENCY: Attempting one final correction before trade');

                        // Use the scorched earth approach
                        this.scorchedEarthAmountReset(amountInput, amount);

                        // Verify again after the emergency correction
                        if (amountInput.isAmountModal && amountInput.amountField) {
                            actualAmount = amountInput.amountField.textContent.replace(/[^0-9.]/g, '');
                        } else if (amountInput.tagName === 'INPUT') {
                            actualAmount = amountInput.value;
                        } else {
                            actualAmount = amountInput.textContent.replace(/[^0-9.]/g, '');
                        }

                        const finalActualNumber = parseFloat(actualAmount);
                        if (!isNaN(finalActualNumber) && Math.abs(expectedNumber - finalActualNumber) < 0.001) {
                            console.log('EMERGENCY CORRECTION SUCCESSFUL: Amount now verified');
                            amountVerified = true;
                        } else {
                            console.error('EMERGENCY CORRECTION FAILED: Cannot proceed with trade due to amount mismatch');

                            // Send failure message to content script
                            window.postMessage({
                                from: 'pocketOptionBot',
                                action: 'tradeExecuted',
                                direction: direction,
                                amount: amount,
                                success: false,
                                error: 'SAFETY CHECK FAILED: Amount mismatch! Expected: ' + expectedNumber + ' Actual: ' + finalActualNumber
                            }, '*');

                            return false;
                        }
                    }
                } else {
                    console.error('SAFETY CHECK FAILED: Invalid amount values');
                    return false;
                }

                // Only proceed if the amount is verified
                if (amountVerified) {
                    // Find the appropriate button
                    let button = null;
                    if (direction.toLowerCase() === 'buy' || direction.toLowerCase() === 'call') {
                        button = this.data.tradingButtons?.buy?.element;
                    } else if (direction.toLowerCase() === 'sell' || direction.toLowerCase() === 'put') {
                        button = this.data.tradingButtons?.sell?.element;
                    }

                    if (button) {
                        console.log('Executing button click for trade');

                        // Create a unique ID for this button click
                        const clickId = `click_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                        console.log(`Button click ID: ${clickId}`);

                        // Simple approach - just click the button once
                        try {
                            // Execute exactly one click
                            console.log(`Executing click with ID: ${clickId}`);
                            button.click();
                        } catch (e) {
                            console.error(`Error during button click: ${e.message}`);
                        }

                        // Send message to content script
                        window.postMessage({
                            from: 'pocketOptionBot',
                            action: 'tradeExecuted',
                            direction: direction,
                            amount: amount,
                            success: true,
                            verifiedAmount: actualAmount,
                            clickId: clickId
                        }, '*');

                        return true;
                    }
                }
            }

            // If we got here, something failed
            window.postMessage({
                from: 'pocketOptionBot',
                action: 'tradeExecuted',
                direction: direction,
                amount: amount,
                success: false,
                error: 'Could not find trading elements'
            }, '*');

            return false;
        } catch (e) {
            console.error('Error executing trade:', e);

            window.postMessage({
                from: 'pocketOptionBot',
                action: 'tradeExecuted',
                direction: direction,
                amount: amount,
                success: false,
                error: e.message
            }, '*');

            return false;
        }
    },

    // Direct function to set amount without executing a trade, with safety checks
    setAmount: function(amount) {
        try {
            console.log('Setting amount directly with safety checks:', amount);

            // Find the amount input
            const amountInput = this.findAmountInput();

            if (amountInput) {
                // First try the scorched earth approach for maximum reliability
                this.scorchedEarthAmountReset(amountInput, amount);

                // Wait a bit for the scorched earth approach to take effect
                setTimeout(() => {
                    // Then set the amount using our enhanced method as a backup
                    const success = this.setAmountValue(amountInput, amount);

                    // Verify the amount was set correctly
                    let actualAmount;

                    // Get the actual amount that was set
                    if (amountInput.isAmountModal && amountInput.amountField) {
                        actualAmount = amountInput.amountField.textContent.replace(/[^0-9.]/g, '');
                    } else if (amountInput.tagName === 'INPUT') {
                        actualAmount = amountInput.value;
                    } else {
                        actualAmount = amountInput.textContent.replace(/[^0-9.]/g, '');
                    }

                    console.log('VERIFICATION - Expected amount:', amount, 'Actual amount:', actualAmount);

                    // Convert both to numbers for comparison
                    const expectedNumber = parseFloat(amount);
                    const actualNumber = parseFloat(actualAmount);

                    let verificationSuccess = false;

                    // Check if the amounts match
                    if (!isNaN(expectedNumber) && !isNaN(actualNumber)) {
                        if (Math.abs(expectedNumber - actualNumber) < 0.001) {
                            console.log('VERIFICATION PASSED: Amount set correctly');
                            verificationSuccess = true;
                        } else {
                            console.error('VERIFICATION FAILED: Amount mismatch! Expected:', expectedNumber, 'Actual:', actualNumber);

                            // Try one more emergency correction
                            this.scorchedEarthAmountReset(amountInput, amount);
                        }
                    }

                    // Send message to content script
                    window.postMessage({
                        from: 'pocketOptionBot',
                        action: 'amountSet',
                        amount: amount,
                        success: success && verificationSuccess,
                        actualAmount: actualAmount,
                        verified: verificationSuccess
                    }, '*');
                }, 200);

                // Return true for now, the actual verification will happen asynchronously
                return true;
            }

            // If we got here, something failed
            window.postMessage({
                from: 'pocketOptionBot',
                action: 'amountSet',
                amount: amount,
                success: false,
                error: 'Could not find amount input'
            }, '*');

            return false;
        } catch (e) {
            console.error('Error setting amount:', e);

            window.postMessage({
                from: 'pocketOptionBot',
                action: 'amountSet',
                amount: amount,
                success: false,
                error: e.message
            }, '*');

            return false;
        }
    },

    // Nuclear approach to completely replace amount - most aggressive method
    nuclearAmountReplacement: function(input, amount) {
        console.log('NUCLEAR AMOUNT REPLACEMENT: Using most aggressive approach for:', amount);

        const amountStr = amount.toString();

        try {
            // Handle different input types
            if (input.isAmountModal && input.amountField) {
                // For modal UI with amount field
                const amountField = input.amountField;
                console.log('Nuclear replacement on amount field:', amountField);

                // Step 1: Focus the element
                amountField.focus();

                // Step 2: Select all content multiple ways
                if (amountField.select) amountField.select();
                if (amountField.setSelectionRange) amountField.setSelectionRange(0, amountField.textContent.length);

                // Step 3: Clear using multiple methods - be extremely aggressive
                amountField.textContent = '';
                amountField.innerHTML = '';
                if (amountField.value !== undefined) amountField.value = '';

                // Additional clearing methods for stubborn fields
                if (amountField.innerText !== undefined) amountField.innerText = '';
                amountField.removeAttribute('value');

                // Try to clear any data attributes that might store the value
                const dataAttrs = ['data-value', 'data-amount', 'data-original-value'];
                dataAttrs.forEach(attr => {
                    if (amountField.hasAttribute(attr)) {
                        amountField.removeAttribute(attr);
                    }
                });

                // Step 4: Dispatch clear events
                amountField.dispatchEvent(new Event('input', { bubbles: true }));
                amountField.dispatchEvent(new Event('change', { bubbles: true }));
                amountField.dispatchEvent(new KeyboardEvent('keydown', { key: 'Delete', bubbles: true }));
                amountField.dispatchEvent(new KeyboardEvent('keydown', { key: 'Backspace', bubbles: true }));

                // Step 5: Wait a tiny moment for clearing to take effect
                setTimeout(() => {
                    // Step 6: Set new value
                    amountField.textContent = amountStr;
                    amountField.innerHTML = amountStr;

                    // Step 7: Dispatch set events
                    amountField.dispatchEvent(new Event('input', { bubbles: true }));
                    amountField.dispatchEvent(new Event('change', { bubbles: true }));
                    amountField.dispatchEvent(new Event('blur', { bubbles: true }));

                    console.log('Nuclear replacement completed on amount field');
                }, 50);

                return true;

            } else if (input.tagName === 'INPUT') {
                // For regular input elements
                console.log('Nuclear replacement on input element:', input);

                // Step 1: Focus the element
                input.focus();

                // Step 2: Select all content
                input.select();
                input.setSelectionRange(0, input.value.length);

                // Step 3: Clear using multiple methods - be extremely aggressive
                input.value = '';
                input.setAttribute('value', '');
                input.removeAttribute('value');

                // Additional clearing for stubborn input fields
                if (input.defaultValue !== undefined) input.defaultValue = '';

                // Try to clear any data attributes that might store the value
                const dataAttrs = ['data-value', 'data-amount', 'data-original-value'];
                dataAttrs.forEach(attr => {
                    if (input.hasAttribute(attr)) {
                        input.removeAttribute(attr);
                    }
                });

                // Step 4: Use execCommand to delete any remaining content
                try {
                    document.execCommand('selectAll');
                    document.execCommand('delete');
                    document.execCommand('removeFormat'); // Remove any formatting that might interfere
                } catch (e) {
                    console.log('execCommand failed, continuing with other methods');
                }

                // Step 5: Dispatch clear events
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                // Step 6: Wait a tiny moment for clearing to take effect
                setTimeout(() => {
                    // Step 7: Set new value
                    input.value = amountStr;
                    input.setAttribute('value', amountStr);

                    // Step 8: Dispatch set events
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                    input.dispatchEvent(new Event('blur', { bubbles: true }));

                    console.log('Nuclear replacement completed on input element');
                }, 50);

                return true;

            } else {
                // For contentEditable or other elements
                console.log('Nuclear replacement on contentEditable element:', input);

                // Step 1: Focus the element
                input.focus();

                // Step 2: Select all content
                const range = document.createRange();
                range.selectNodeContents(input);
                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);

                // Step 3: Clear using multiple methods
                input.textContent = '';
                input.innerHTML = '';

                // Step 4: Use execCommand to delete any remaining content
                try {
                    document.execCommand('selectAll');
                    document.execCommand('delete');
                } catch (e) {
                    console.log('execCommand failed, continuing with other methods');
                }

                // Step 5: Dispatch clear events
                input.dispatchEvent(new Event('input', { bubbles: true }));
                input.dispatchEvent(new Event('change', { bubbles: true }));

                // Step 6: Wait a tiny moment for clearing to take effect
                setTimeout(() => {
                    // Step 7: Set new value
                    input.textContent = amountStr;
                    input.innerHTML = amountStr;

                    // Step 8: Dispatch set events
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));
                    input.dispatchEvent(new Event('blur', { bubbles: true }));

                    console.log('Nuclear replacement completed on contentEditable element');
                }, 50);

                return true;
            }

        } catch (e) {
            console.error('Error in nuclear amount replacement:', e);
            return false;
        }
    },

    // Special function to handle the case where the bot is modifying existing numbers instead of replacing them
    forceCompleteAmountReplacement: function(amount) {
        console.log('FORCE COMPLETE REPLACEMENT: Attempting to completely replace any existing amount with:', amount);

        try {
            // This is a specialized function that focuses specifically on the issue where
            // the bot is modifying existing numbers (e.g., changing 14.50 to 14.51) instead of replacing them

            // First, find the amount input using our existing method
            const amountInput = this.findAmountInput();
            if (amountInput) {
                console.log('Found amount input for force replacement:', amountInput);

                // Use the nuclear approach - completely clear and replace
                this.nuclearAmountReplacement(amountInput, amount);
                return true;
            }

            // If that doesn't work, inject a script that will aggressively target all possible amount fields
            const script = document.createElement('script');
            script.textContent = `
                (function() {
                    try {
                        console.log('FORCE REPLACEMENT: Starting specialized DOM manipulation');

                        // Function to completely replace an element's content
                        function nukeAndReplaceElement(el, newValue) {
                            // Get parent
                            const parent = el.parentElement;
                            if (!parent) return;

                            // Save attributes
                            const className = el.className;
                            const id = el.id;
                            const tagName = el.tagName;

                            // Remove the element
                            parent.removeChild(el);

                            // Create a new element of the same type
                            const newEl = document.createElement(tagName);
                            newEl.className = className;
                            if (id) newEl.id = id;

                            // Set the value appropriately
                            if (tagName === 'INPUT') {
                                newEl.value = newValue;
                                newEl.setAttribute('value', newValue);
                            } else {
                                newEl.textContent = '$' + newValue;
                            }

                            // Add it back
                            parent.appendChild(newEl);

                            // Trigger events
                            newEl.dispatchEvent(new Event('input', { bubbles: true }));
                            newEl.dispatchEvent(new Event('change', { bubbles: true }));

                            return newEl;
                        }

                        // Find ALL possible amount fields
                        const amountFields = document.querySelectorAll('.amount-field, [class*="amount"], [class*="bet"], [id*="amount"], [id*="bet"], [name*="amount"], [name*="bet"]');
                        console.log('Found ' + amountFields.length + ' potential amount fields');

                        // Process each field
                        for (const field of amountFields) {
                            try {
                                // Check if it's an input or another element
                                if (field.tagName === 'INPUT') {
                                    // For input elements, completely clear and set
                                    field.value = '';
                                    field.setAttribute('value', '');
                                    field.dispatchEvent(new Event('input', { bubbles: true }));

                                    // Wait a tiny bit
                                    setTimeout(() => {
                                        field.value = '${amount}';
                                        field.setAttribute('value', '${amount}');
                                        field.dispatchEvent(new Event('input', { bubbles: true }));
                                        field.dispatchEvent(new Event('change', { bubbles: true }));
                                    }, 50);
                                } else {
                                    // For other elements, try to completely replace the content

                                    // First try to remove all child nodes
                                    while (field.firstChild) {
                                        field.removeChild(field.firstChild);
                                    }

                                    // Set to just the currency symbol
                                    field.textContent = '$';
                                    field.innerHTML = '$';
                                    field.dispatchEvent(new Event('input', { bubbles: true }));

                                    // Wait a tiny bit
                                    setTimeout(() => {
                                        // Then set the new value
                                        field.textContent = '$${amount}';
                                        field.innerHTML = '$${amount}';
                                        field.dispatchEvent(new Event('input', { bubbles: true }));
                                        field.dispatchEvent(new Event('change', { bubbles: true }));
                                    }, 50);

                                    // Also try the nuclear approach
                                    setTimeout(() => {
                                        nukeAndReplaceElement(field, '${amount}');
                                    }, 100);
                                }
                            } catch (e) {
                                console.error('Error processing field:', e);
                            }
                        }

                        // Also try to find the specific amount field that might be causing issues
                        const amountField = document.querySelector('.amount-field');
                        if (amountField) {
                            console.log('Found primary amount field, applying special handling');

                            // Try to completely recreate this element
                            const newField = nukeAndReplaceElement(amountField, '${amount}');

                            // Verify the result
                            setTimeout(() => {
                                const verifyField = document.querySelector('.amount-field');
                                if (verifyField) {
                                    const value = verifyField.textContent.replace(/[^0-9.]/g, '');
                                    console.log('Verification after special handling:', value);

                                    if (value !== '${amount}') {
                                        console.error('Special handling failed, trying one last approach');

                                        // Try one more extreme approach - modify the parent's innerHTML
                                        const parent = verifyField.parentElement;
                                        if (parent) {
                                            const html = parent.innerHTML;
                                            // Replace any number pattern with our desired amount
                                            const newHtml = html.replace(/\\$?\\d+(\\.\\d+)?/g, '$${amount}');
                                            parent.innerHTML = newHtml;
                                        }
                                    }
                                }
                            }, 150);
                        }

                        console.log('FORCE REPLACEMENT: Completed specialized DOM manipulation');
                    } catch (e) {
                        console.error('Error in force replacement script:', e);
                    }
                })();
            `;
            document.head.appendChild(script);
            document.head.removeChild(script);

            return true;
        } catch (e) {
            console.error('Error in force complete replacement:', e);
            return false;
        }
    },

    // Function to directly modify the DOM using JavaScript injection
    injectScript: function(scriptContent) {
        try {
            const script = document.createElement('script');
            script.textContent = scriptContent;
            document.head.appendChild(script);
            document.head.removeChild(script);
            return true;
        } catch (e) {
            console.error('Error injecting script:', e);
            return false;
        }
    }
};

// Listen for messages from the content script
window.addEventListener('message', function(event) {
    // Only accept messages from the same frame
    if (event.source !== window) return;

    const message = event.data;

    if (message && message.action === 'initializePocketOptionBot') {
        console.log('Initializing Pocket Option Bot');

        // Initialize the bot
        window.pocketOptionBot.getBalance();
        window.pocketOptionBot.findTradingButtons();

        // Set up a periodic check for balance changes, but very infrequently
        setInterval(() => {
            window.pocketOptionBot.getBalance();
        }, 60000); // Check only once per minute

        // Notify that the bot is ready
        window.postMessage({
            from: 'pocketOptionBot',
            action: 'botReady'
        }, '*');
    }
    else if (message && message.action === 'executeTrade') {
        // Execute a trade
        window.pocketOptionBot.executeTrade(message.direction, message.amount);
    }
    else if (message && message.action === 'setAmount') {
        // Set the amount without executing a trade
        window.pocketOptionBot.setAmount(message.amount);
    }
    else if (message && message.action === 'forceCompleteAmountReplacement') {
        // Use the specialized function for completely replacing amounts
        window.pocketOptionBot.forceCompleteAmountReplacement(message.amount);
    }
    else if (message && message.action === 'nuclearAmountReplacement') {
        // Use the nuclear approach for the most aggressive amount replacement
        console.log('Received nuclear amount replacement request:', message.amount);

        const amountInput = window.pocketOptionBot.findAmountInput();
        if (amountInput) {
            const success = window.pocketOptionBot.nuclearAmountReplacement(amountInput, message.amount);

            // Send response back
            window.postMessage({
                from: 'pocketOptionBot',
                action: 'nuclearAmountSet',
                success: success,
                amount: message.amount
            }, '*');
        } else {
            console.error('Could not find amount input for nuclear replacement');
            window.postMessage({
                from: 'pocketOptionBot',
                action: 'nuclearAmountSet',
                success: false,
                error: 'Could not find amount input'
            }, '*');
        }
    }
    else if (message && message.action === 'humanLikeAmountReplacement') {
        // Add our new human-like approach
        // This will simulate how a human would select all text and paste in one operation
        try {
            console.log('HUMAN-LIKE APPROACH: Received request to set amount to', message.amount);

            // Find the amount input
            const amountInput = window.pocketOptionBot.findAmountInput();
            if (!amountInput) {
                console.error('Could not find amount input for human-like replacement');
                window.postMessage({
                    from: 'pocketOptionBot',
                    action: 'amountSet',
                    success: false,
                    error: 'Could not find amount input'
                }, '*');
                return;
            }

            // Function to select all text and replace in one operation
            const selectAllAndReplace = (element, newValue) => {
                console.log('Using aggressive clear-and-replace on element:', element);

                // For input elements
                if (element.tagName === 'INPUT') {
                    // Step 1: Focus the element
                    element.focus();

                    // Step 2: Completely clear the field using multiple methods
                    element.value = '';
                    element.setAttribute('value', '');

                    // Step 3: Select all remaining content (if any) and delete it
                    element.select();
                    element.setSelectionRange(0, element.value.length);

                    // Step 4: Dispatch input event to clear any cached values
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));

                    // Step 5: Wait a tiny bit for the UI to update
                    setTimeout(() => {
                        // Step 6: Set the new value
                        element.value = newValue;
                        element.setAttribute('value', newValue);

                        // Step 7: Dispatch events to notify the UI
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                        element.dispatchEvent(new Event('keyup', { bubbles: true }));

                        // Step 8: Verify the value was set correctly
                        console.log('After aggressive setting, input value is:', element.value);

                        // Send success message with verification
                        window.postMessage({
                            from: 'pocketOptionBot',
                            action: 'amountSet',
                            success: true,
                            verified: element.value === newValue,
                            actualAmount: element.value
                        }, '*');
                    }, 100);

                    return true;
                }

                // For contentEditable elements or other elements
                else {
                    // For contentEditable elements, use a more aggressive approach
                    console.log('Handling contentEditable or other element type');

                    try {
                        // Step 1: Focus the element
                        element.focus();

                        // Step 2: Completely clear the element using multiple methods
                        element.innerHTML = '';
                        element.textContent = '';

                        // Step 3: Remove all child nodes
                        while (element.firstChild) {
                            element.removeChild(element.firstChild);
                        }

                        // Step 4: Dispatch events to clear any cached values
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));

                        // Step 5: Force a redraw
                        element.style.display = 'none';
                        element.offsetHeight; // Force reflow
                        element.style.display = '';

                        // Step 6: Wait a tiny bit for the UI to update
                        setTimeout(() => {
                            // Step 7: Set the new value with currency symbol
                            const valueWithCurrency = '$' + newValue;
                            element.textContent = valueWithCurrency;
                            element.innerHTML = valueWithCurrency;

                            // Step 8: Dispatch events to notify the UI
                            element.dispatchEvent(new Event('input', { bubbles: true }));
                            element.dispatchEvent(new Event('change', { bubbles: true }));
                            element.dispatchEvent(new Event('keyup', { bubbles: true }));

                            // Step 9: Verify the value was set correctly
                            const actualValue = element.textContent.replace(/[^0-9.]/g, '');
                            console.log('After aggressive setting, element content is:', actualValue);

                            // Send success message with verification
                            window.postMessage({
                                from: 'pocketOptionBot',
                                action: 'amountSet',
                                success: true,
                                verified: actualValue === newValue,
                                actualAmount: actualValue
                            }, '*');
                        }, 100);

                    } catch (e) {
                        console.log('Error in aggressive clearing and setting content:', e);
                        // Fallback to direct content setting
                        element.textContent = '$' + newValue;
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        element.dispatchEvent(new Event('change', { bubbles: true }));

                        // Send fallback success message
                        window.postMessage({
                            from: 'pocketOptionBot',
                            action: 'amountSet',
                            success: true,
                            verified: false,
                            actualAmount: element.textContent.replace(/[^0-9.]/g, '')
                        }, '*');
                    }

                    return true;
                }
            };

            // Handle different types of amount inputs
            if (amountInput.isAmountModal && amountInput.amountField) {
                // For modal UI
                selectAllAndReplace(amountInput.amountField, message.amount);
            }
            else if (amountInput.isAmountTrigger && amountInput.element) {
                // For trigger elements, click first then find the field
                amountInput.element.click();
                setTimeout(() => {
                    const amountField = document.querySelector('.amount-field') ||
                                      document.querySelector('.multiply-field') ||
                                      document.querySelector('[contenteditable="true"]');

                    if (amountField) {
                        selectAllAndReplace(amountField, message.amount);
                    } else {
                        console.error('Could not find amount field after clicking trigger');
                        window.postMessage({
                            from: 'pocketOptionBot',
                            action: 'amountSet',
                            success: false,
                            error: 'Could not find amount field after clicking trigger'
                        }, '*');
                    }
                }, 200);
            }
            else {
                // For regular input or contentEditable elements
                selectAllAndReplace(amountInput, message.amount);
            }
        } catch (e) {
            console.error('Error in human-like amount replacement:', e);
            window.postMessage({
                from: 'pocketOptionBot',
                action: 'amountSet',
                success: false,
                error: e.message
            }, '*');
        }
    }
    else if (message && message.action === 'injectScript') {
        // Inject custom script
        window.pocketOptionBot.injectScript(message.scriptContent);
    }
    else if (message && message.action === 'findAmountInput') {
        // Find the amount input and report back
        const amountInput = window.pocketOptionBot.findAmountInput();
        window.postMessage({
            from: 'pocketOptionBot',
            action: 'amountInputFound',
            success: !!amountInput,
            details: amountInput ? {
                tagName: amountInput.tagName,
                id: amountInput.id,
                className: amountInput.className,
                type: amountInput.type,
                value: amountInput.value || amountInput.textContent
            } : null
        }, '*');
    }
});

// Notify that the script has loaded
console.log('Pocket Option Bot page script loaded');
